# Freqtrade Orderflow Strategy

An advanced orderflow trading strategy for Freqtrade that leverages market microstructure data to make informed trading decisions.

## Features

- **Delta Analysis**: Real-time analysis of buy/sell pressure through delta calculations
- **Cumulative Volume Delta (CVD)**: Trend analysis and divergence detection using CVD
- **Imbalance Detection**: Identification of bid/ask imbalances and stacked imbalance zones
- **Footprint Analysis**: Price-level volume analysis for dominant trading levels
- **Multi-Indicator Confluence**: Weighted scoring system combining multiple orderflow signals
- **Dynamic Risk Management**: Position sizing and stop-loss adjustment based on signal strength

## Requirements

- Freqtrade 2024.1+
- Exchange with public trade data support (Binance, etc.)
- Sufficient system resources for orderflow data processing

## Quick Start

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Configure your exchange API keys in `config.json`

3. Download historical trade data:
```bash
freqtrade download-data -p BTC/USDT:USDT --timerange 20240101- --trading-mode futures --timeframes 5m --dl-trades
```

4. Run backtesting:
```bash
freqtrade backtesting --strategy OrderflowStrategy --config config.json
```

## Configuration

The strategy requires orderflow data to be enabled in your Freqtrade configuration. See `config.json` for the complete setup.

## Strategy Parameters

Key parameters that can be optimized:
- `delta_period`: Period for delta moving average (default: 10)
- `delta_threshold`: Threshold for strong delta signals (default: 2.0)
- `cvd_period`: Period for CVD analysis (default: 20)
- `min_confluence_score`: Minimum score for trade entry (default: 75)

## Performance Notes

- Initial startup may be slow due to orderflow data download
- Increased memory usage compared to standard strategies
- Recommended minimum 8GB RAM for stable operation

## Testing and Backtesting

### Quick Start

1. **Validate Data Quality**:
   ```bash
   python validate_data.py
   ```

2. **Download Data and Run Backtest**:
   ```bash
   python run_backtest.py --download --timerange 20240101-20241201
   ```

3. **Run Parameter Optimization**:
   ```bash
   python run_backtest.py --optimize
   ```

### Manual Testing

1. **Validate Configuration**:
   ```bash
   freqtrade test-pairlist --config config_backtest.json
   ```

2. **Download Historical Data**:
   ```bash
   # Download OHLCV data
   freqtrade download-data --config config_backtest.json --timerange 20240101-20241201 --timeframes 5m

   # Download trades data for orderflow analysis
   freqtrade download-data --config config_backtest.json --timerange 20240101-20241201 --dl-trades --data-format-trades jsongz
   ```

3. **Run Backtest**:
   ```bash
   freqtrade backtesting --config config_backtest.json --strategy OrderflowStrategy --timerange 20240101-20241201 --export trades
   ```

### Expected Results

With proper orderflow data, expect:
- **Win Rate**: 55-65%
- **Profit Factor**: 1.3-1.8
- **Max Drawdown**: <15%
- **Sharpe Ratio**: >1.0

## Documentation

### 📚 Complete Documentation Suite

- **[Usage Guide](USAGE_GUIDE.md)**: Comprehensive user guide with setup, configuration, and usage instructions
- **[Technical Documentation](TECHNICAL_DOCS.md)**: Detailed technical architecture, algorithms, and implementation details
- **[Configuration Examples](CONFIG_EXAMPLES.md)**: Ready-to-use configuration examples for different trading scenarios

### 🔧 Validation Tools

- **`validate_strategy_syntax.py`**: Validates Python syntax and strategy structure
- **`validate_data.py`**: Checks data quality and availability
- **`test_strategy.py`**: Functional testing (requires freqtrade installation)
- **`run_backtest.py`**: Automated backtesting with data download

### 📊 Strategy Features Summary

| Feature | Implementation | Status |
|---------|---------------|--------|
| **Delta Analysis** | Comprehensive delta indicators with momentum and divergence | ✅ Complete |
| **CVD Analysis** | Multi-timeframe CVD with trend and regime detection | ✅ Complete |
| **Imbalance Detection** | Bid/ask imbalance with stacking and clustering | ✅ Complete |
| **Footprint Analysis** | Volume profile, POC, and value area analysis | ✅ Complete |
| **Confluence Scoring** | Weighted multi-indicator scoring system | ✅ Complete |
| **Dynamic Risk Management** | Signal-based position sizing and stop losses | ✅ Complete |
| **Performance Optimization** | Caching, memory management, and monitoring | ✅ Complete |
| **Comprehensive Testing** | Syntax validation, data validation, backtesting | ✅ Complete |

### 🎯 Quick Start Commands

```bash
# 1. Validate everything
python validate_strategy_syntax.py
python validate_data.py

# 2. Download data and backtest
python run_backtest.py --download --timerange 20240101-20241201

# 3. Optimize parameters
python run_backtest.py --optimize

# 4. Manual freqtrade commands
freqtrade backtesting --config config_backtest.json --strategy OrderflowStrategy --timerange 20240101-20241201
```

## License

This project is licensed under the MIT License.
