# Freqtrade Orderflow Strategy

An advanced orderflow trading strategy for Freqtrade that leverages market microstructure data to make informed trading decisions.

## Features

- **Delta Analysis**: Real-time analysis of buy/sell pressure through delta calculations
- **Cumulative Volume Delta (CVD)**: Trend analysis and divergence detection using CVD
- **Imbalance Detection**: Identification of bid/ask imbalances and stacked imbalance zones
- **Footprint Analysis**: Price-level volume analysis for dominant trading levels
- **Multi-Indicator Confluence**: Weighted scoring system combining multiple orderflow signals
- **Dynamic Risk Management**: Position sizing and stop-loss adjustment based on signal strength

## Requirements

- Freqtrade 2024.1+
- Exchange with public trade data support (Binance, etc.)
- Sufficient system resources for orderflow data processing

## Quick Start

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Configure your exchange API keys in `config.json`

3. Download historical trade data:
```bash
freqtrade download-data -p BTC/USDT:USDT --timerange 20240101- --trading-mode futures --timeframes 5m --dl-trades
```

4. Run backtesting:
```bash
freqtrade backtesting --strategy OrderflowStrategy --config config.json
```

## Configuration

The strategy requires orderflow data to be enabled in your Freqtrade configuration. See `config.json` for the complete setup.

## Strategy Parameters

Key parameters that can be optimized:
- `delta_period`: Period for delta moving average (default: 10)
- `delta_threshold`: Threshold for strong delta signals (default: 2.0)
- `cvd_period`: Period for CVD analysis (default: 20)
- `min_confluence_score`: Minimum score for trade entry (default: 75)

## Performance Notes

- Initial startup may be slow due to orderflow data download
- Increased memory usage compared to standard strategies
- Recommended minimum 8GB RAM for stable operation

## License

This project is licensed under the MIT License.
