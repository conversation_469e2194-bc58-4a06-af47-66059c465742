#!/usr/bin/env python3
"""
Syntax validation script for OrderflowStrategy
Validates Python syntax and basic structure without requiring freqtrade
"""

import ast
import sys
from pathlib import Path

def validate_python_syntax(file_path):
    """Validate Python syntax of the strategy file"""
    print(f"🔍 Validating syntax for {file_path}...")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # Parse the AST
        tree = ast.parse(source_code, filename=file_path)
        print("✅ Python syntax is valid")
        
        return tree, source_code
        
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        print(f"   Line {e.lineno}: {e.text}")
        return None, None
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return None, None

def analyze_strategy_structure(tree, source_code):
    """Analyze the structure of the strategy class"""
    print("\n📋 Analyzing strategy structure...")
    
    classes = []
    functions = []
    imports = []
    
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            classes.append(node.name)
        elif isinstance(node, ast.FunctionDef):
            functions.append(node.name)
        elif isinstance(node, ast.Import):
            for alias in node.names:
                imports.append(alias.name)
        elif isinstance(node, ast.ImportFrom):
            module = node.module or ""
            for alias in node.names:
                imports.append(f"{module}.{alias.name}")
    
    print(f"✅ Found {len(classes)} classes: {classes}")
    print(f"✅ Found {len(functions)} functions")
    print(f"✅ Found {len(imports)} imports")
    
    # Check for required strategy methods
    required_methods = [
        'populate_indicators',
        'populate_entry_trend', 
        'populate_exit_trend'
    ]
    
    missing_methods = [method for method in required_methods if method not in functions]
    if missing_methods:
        print(f"⚠️  Missing required methods: {missing_methods}")
    else:
        print("✅ All required strategy methods present")
    
    # Check for orderflow-specific methods
    orderflow_methods = [
        '_calculate_delta_indicators',
        '_calculate_cvd_indicators',
        '_calculate_imbalance_indicators',
        '_calculate_footprint_indicators',
        '_calculate_confluence_score'
    ]
    
    present_orderflow_methods = [method for method in orderflow_methods if method in functions]
    print(f"✅ Orderflow methods present: {len(present_orderflow_methods)}/{len(orderflow_methods)}")
    
    # Check for performance optimization methods
    performance_methods = [
        '_cached_calculation',
        '_cleanup_cache',
        'get_performance_stats'
    ]
    
    present_performance_methods = [method for method in performance_methods if method in functions]
    print(f"✅ Performance methods present: {len(present_performance_methods)}/{len(performance_methods)}")
    
    return {
        'classes': classes,
        'functions': functions,
        'imports': imports,
        'missing_methods': missing_methods,
        'orderflow_methods': present_orderflow_methods,
        'performance_methods': present_performance_methods
    }

def check_code_quality(source_code):
    """Check basic code quality metrics"""
    print("\n🔧 Checking code quality...")
    
    lines = source_code.split('\n')
    total_lines = len(lines)
    code_lines = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
    comment_lines = len([line for line in lines if line.strip().startswith('#')])
    blank_lines = total_lines - code_lines - comment_lines
    
    print(f"📊 Code metrics:")
    print(f"  Total lines: {total_lines}")
    print(f"  Code lines: {code_lines}")
    print(f"  Comment lines: {comment_lines}")
    print(f"  Blank lines: {blank_lines}")
    print(f"  Comment ratio: {comment_lines/max(code_lines, 1)*100:.1f}%")
    
    # Check for very long lines
    long_lines = [i+1 for i, line in enumerate(lines) if len(line) > 120]
    if long_lines:
        print(f"⚠️  Lines longer than 120 chars: {len(long_lines)} (lines: {long_lines[:5]}{'...' if len(long_lines) > 5 else ''})")
    else:
        print("✅ No excessively long lines")
    
    # Check for TODO/FIXME comments
    todo_lines = [i+1 for i, line in enumerate(lines) if 'TODO' in line.upper() or 'FIXME' in line.upper()]
    if todo_lines:
        print(f"📝 TODO/FIXME comments found on lines: {todo_lines}")
    
    return {
        'total_lines': total_lines,
        'code_lines': code_lines,
        'comment_lines': comment_lines,
        'long_lines': len(long_lines),
        'todo_lines': len(todo_lines)
    }

def validate_configuration_files():
    """Validate configuration files"""
    print("\n⚙️  Validating configuration files...")
    
    config_files = [
        'config.json',
        'config_backtest.json'
    ]
    
    valid_configs = 0
    
    for config_file in config_files:
        if Path(config_file).exists():
            try:
                import json
                with open(config_file, 'r') as f:
                    config = json.load(f)
                print(f"✅ {config_file}: Valid JSON")
                
                # Check for required keys
                required_keys = ['exchange', 'timeframe', 'strategy']
                missing_keys = [key for key in required_keys if key not in config]
                if missing_keys:
                    print(f"⚠️  {config_file}: Missing keys: {missing_keys}")
                else:
                    print(f"✅ {config_file}: All required keys present")
                
                valid_configs += 1
                
            except json.JSONDecodeError as e:
                print(f"❌ {config_file}: Invalid JSON - {e}")
            except Exception as e:
                print(f"❌ {config_file}: Error - {e}")
        else:
            print(f"⚠️  {config_file}: File not found")
    
    return valid_configs

def generate_validation_report(strategy_analysis, code_quality):
    """Generate comprehensive validation report"""
    print("\n" + "="*60)
    print("📋 STRATEGY VALIDATION REPORT")
    print("="*60)
    
    # Overall status
    issues = len(strategy_analysis['missing_methods'])
    if issues == 0:
        print("✅ OVERALL STATUS: PASSED")
    else:
        print(f"⚠️  OVERALL STATUS: PASSED WITH {issues} WARNINGS")
    
    # Strategy completeness
    print(f"\n🎯 STRATEGY COMPLETENESS:")
    print(f"  Required methods: {'✅ Complete' if not strategy_analysis['missing_methods'] else '⚠️  Incomplete'}")
    print(f"  Orderflow features: {len(strategy_analysis['orderflow_methods'])}/5 implemented")
    print(f"  Performance features: {len(strategy_analysis['performance_methods'])}/3 implemented")
    
    # Code quality
    print(f"\n📊 CODE QUALITY:")
    print(f"  Total lines: {code_quality['total_lines']}")
    print(f"  Comment ratio: {code_quality['comment_lines']/max(code_quality['code_lines'], 1)*100:.1f}%")
    print(f"  Code style: {'✅ Good' if code_quality['long_lines'] == 0 else '⚠️  Some long lines'}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if strategy_analysis['missing_methods']:
        print(f"  • Implement missing methods: {strategy_analysis['missing_methods']}")
    
    if len(strategy_analysis['orderflow_methods']) < 5:
        print(f"  • Complete orderflow analysis implementation")
    
    if len(strategy_analysis['performance_methods']) < 3:
        print(f"  • Add performance optimization features")
    
    if code_quality['long_lines'] > 0:
        print(f"  • Consider breaking up long lines for readability")
    
    if code_quality['comment_lines']/max(code_quality['code_lines'], 1) < 0.1:
        print(f"  • Add more documentation and comments")
    
    print(f"  • Strategy is ready for testing with freqtrade")
    print(f"  • Run backtests to validate trading logic")
    
    print("\n" + "="*60)

def main():
    print("🧪 ORDERFLOW STRATEGY SYNTAX VALIDATION")
    print("="*60)
    
    strategy_file = Path("strategies/OrderflowStrategy.py")
    
    if not strategy_file.exists():
        print(f"❌ Strategy file not found: {strategy_file}")
        return
    
    # Validate syntax
    tree, source_code = validate_python_syntax(strategy_file)
    if not tree:
        return
    
    # Analyze structure
    strategy_analysis = analyze_strategy_structure(tree, source_code)
    
    # Check code quality
    code_quality = check_code_quality(source_code)
    
    # Validate configurations
    validate_configuration_files()
    
    # Generate report
    generate_validation_report(strategy_analysis, code_quality)

if __name__ == "__main__":
    main()
