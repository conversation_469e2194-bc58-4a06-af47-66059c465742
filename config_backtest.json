{"max_open_trades": 3, "stake_currency": "USDT", "stake_amount": 1000, "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "timeframe": "5m", "dry_run": false, "cancel_open_orders_on_exit": false, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": ["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT"], "pair_blacklist": ["BNB/BTC"]}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "orderflow": {"use_public_trades": true, "cache_size": 1000, "scale": 0.5}, "pairlists": [{"method": "StaticPairList"}], "telegram": {"enabled": false}, "api_server": {"enabled": false}, "bot_name": "OrderflowStrategy", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}, "dataformat_ohlcv": "json", "dataformat_trades": "jsongz", "backtest": {"timerange": "20240101-20241201", "max_open_trades": 3, "stake_amount": 1000, "enable_protections": true, "startup_candle_count": 200, "fee": 0.001, "trade_source": "file", "export": "trades", "exportfilename": "backtest_results/orderflow_backtest.json", "breakdown": ["day", "week", "month"], "cache": "day"}, "protections": [{"method": "<PERSON><PERSON><PERSON><PERSON>", "lookback_period_candles": 60, "trade_limit": 4, "stop_duration_candles": 60, "only_per_pair": false}, {"method": "MaxDrawdown", "lookback_period_candles": 200, "trade_limit": 20, "stop_duration_candles": 100, "max_allowed_drawdown": 0.2}, {"method": "LowProfitPairs", "lookback_period_candles": 400, "trade_limit": 20, "stop_duration_candles": 60, "required_profit": 0.02}], "strategy": "OrderflowStrategy", "strategy_path": "strategies/", "orderflow_strategy_params": {"delta_period": 20, "cvd_period": 50, "min_confluence_score": 60, "strong_signal_threshold": 80, "imbalance_threshold": 3.0, "volume_threshold": 1.5, "divergence_lookback": 20, "exhaustion_threshold": 0.8}, "logging": {"verbosity": 2, "logfile": "logs/orderflow_backtest.log"}}