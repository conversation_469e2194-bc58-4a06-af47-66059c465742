#!/usr/bin/env python3
"""
Comprehensive backtesting script for OrderflowStrategy
Automates data download, backtesting, and result analysis
"""

import os
import sys
import json
import subprocess
import argparse
from datetime import datetime, timedelta
from pathlib import Path

class OrderflowBacktester:
    def __init__(self, config_file="config_backtest.json"):
        self.config_file = config_file
        self.results_dir = Path("backtest_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # Load configuration
        with open(config_file, 'r') as f:
            self.config = json.load(f)
    
    def download_data(self, timerange=None, pairs=None):
        """Download historical data for backtesting"""
        print("📊 Downloading historical data...")
        
        if timerange is None:
            timerange = self.config.get('backtest', {}).get('timerange', '20240101-20241201')
        
        if pairs is None:
            pairs = self.config['exchange']['pair_whitelist']
        
        # Download OHLCV data
        cmd = [
            'freqtrade', 'download-data',
            '--config', self.config_file,
            '--timerange', timerange,
            '--timeframes', self.config['timeframe'],
            '--pairs'
        ] + pairs
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Error downloading data: {result.stderr}")
            return False
        
        print("✅ OHLCV data downloaded successfully")
        
        # Download trades data for orderflow analysis
        cmd_trades = [
            'freqtrade', 'download-data',
            '--config', self.config_file,
            '--timerange', timerange,
            '--data-format-trades', 'jsongz',
            '--dl-trades',
            '--pairs'
        ] + pairs
        
        print(f"Running: {' '.join(cmd_trades)}")
        result = subprocess.run(cmd_trades, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"⚠️  Warning: Could not download trades data: {result.stderr}")
            print("Orderflow analysis may be limited without trades data")
        else:
            print("✅ Trades data downloaded successfully")
        
        return True
    
    def run_backtest(self, strategy_params=None):
        """Run the backtest with specified parameters"""
        print("🚀 Running backtest...")
        
        # Create timestamp for this run
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = self.results_dir / f"orderflow_backtest_{timestamp}.json"
        
        cmd = [
            'freqtrade', 'backtesting',
            '--config', self.config_file,
            '--strategy', 'OrderflowStrategy',
            '--export', 'trades',
            '--exportfilename', str(results_file),
            '--breakdown', 'day', 'week', 'month',
            '--cache', 'day'
        ]
        
        # Add strategy parameters if provided
        if strategy_params:
            for param, value in strategy_params.items():
                cmd.extend(['--strategy-list', f'{param}={value}'])
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Backtest failed: {result.stderr}")
            return None
        
        print("✅ Backtest completed successfully")
        print(result.stdout)
        
        return results_file
    
    def analyze_results(self, results_file):
        """Analyze backtest results"""
        print("📈 Analyzing results...")
        
        if not results_file or not results_file.exists():
            print("❌ No results file found")
            return
        
        # Load results
        try:
            with open(results_file, 'r') as f:
                results = json.load(f)
        except Exception as e:
            print(f"❌ Error loading results: {e}")
            return
        
        # Extract key metrics
        strategy_stats = results.get('strategy', {}).get('OrderflowStrategy', {})
        
        print("\n" + "="*50)
        print("📊 BACKTEST RESULTS SUMMARY")
        print("="*50)
        
        # Performance metrics
        total_trades = strategy_stats.get('total_trades', 0)
        profit_total = strategy_stats.get('profit_total_abs', 0)
        profit_percent = strategy_stats.get('profit_total', 0) * 100
        win_rate = strategy_stats.get('wins', 0) / max(total_trades, 1) * 100
        
        print(f"Total Trades: {total_trades}")
        print(f"Total Profit: ${profit_total:.2f} ({profit_percent:.2f}%)")
        print(f"Win Rate: {win_rate:.1f}%")
        print(f"Max Drawdown: {strategy_stats.get('max_drawdown', 0)*100:.2f}%")
        print(f"Sharpe Ratio: {strategy_stats.get('sharpe', 0):.2f}")
        print(f"Calmar Ratio: {strategy_stats.get('calmar', 0):.2f}")
        
        # Trade analysis
        avg_profit = strategy_stats.get('profit_mean', 0) * 100
        best_trade = strategy_stats.get('best_pair', {}).get('profit_sum', 0) * 100
        worst_trade = strategy_stats.get('worst_pair', {}).get('profit_sum', 0) * 100
        
        print(f"\nAverage Trade: {avg_profit:.2f}%")
        print(f"Best Trade: {best_trade:.2f}%")
        print(f"Worst Trade: {worst_trade:.2f}%")
        
        # Entry tag analysis
        if 'results_per_enter_tag' in results:
            print("\n📝 Entry Signal Performance:")
            for tag, stats in results['results_per_enter_tag'].items():
                tag_profit = stats.get('profit_total', 0) * 100
                tag_trades = stats.get('total_trades', 0)
                tag_winrate = stats.get('wins', 0) / max(tag_trades, 1) * 100
                print(f"  {tag}: {tag_trades} trades, {tag_profit:.2f}% profit, {tag_winrate:.1f}% win rate")
        
        print("\n" + "="*50)
        
        return results
    
    def run_parameter_optimization(self, param_ranges):
        """Run parameter optimization"""
        print("🔧 Running parameter optimization...")
        
        cmd = [
            'freqtrade', 'hyperopt',
            '--config', self.config_file,
            '--strategy', 'OrderflowStrategy',
            '--hyperopt-loss', 'SharpeHyperOptLoss',
            '--epochs', '100',
            '--spaces', 'buy', 'sell'
        ]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Hyperopt failed: {result.stderr}")
            return False
        
        print("✅ Parameter optimization completed")
        print(result.stdout)
        return True

def main():
    parser = argparse.ArgumentParser(description='Run OrderflowStrategy backtest')
    parser.add_argument('--download', action='store_true', help='Download data before backtesting')
    parser.add_argument('--timerange', type=str, help='Timerange for data download (e.g., 20240101-20241201)')
    parser.add_argument('--optimize', action='store_true', help='Run parameter optimization')
    parser.add_argument('--config', type=str, default='config_backtest.json', help='Config file to use')
    
    args = parser.parse_args()
    
    backtester = OrderflowBacktester(args.config)
    
    # Download data if requested
    if args.download:
        if not backtester.download_data(args.timerange):
            print("❌ Data download failed, exiting")
            sys.exit(1)
    
    # Run optimization if requested
    if args.optimize:
        backtester.run_parameter_optimization({})
        return
    
    # Run backtest
    results_file = backtester.run_backtest()
    
    # Analyze results
    if results_file:
        backtester.analyze_results(results_file)

if __name__ == "__main__":
    main()
