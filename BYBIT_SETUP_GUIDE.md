# Bybit Futures USDT Setup Guide for OrderflowStrategy

## Quick Start for Bybit Futures

### 1. Bybit API Setup

1. **Create Bybit Account**: Sign up at [bybit.com](https://www.bybit.com)
2. **Enable API Access**:
   - Go to Account & Security → API Management
   - Create new API key with these permissions:
     - ✅ Read-Write (for trading)
     - ✅ Derivatives (for futures trading)
     - ✅ Spot (optional, for spot trading)
   - **Important**: Restrict IP access for security

3. **API Key Configuration**:
   ```json
   {
       "exchange": {
           "name": "bybit",
           "key": "your_bybit_api_key",
           "secret": "your_bybit_api_secret"
       }
   }
   ```

### 2. Use Bybit Configuration

Copy the provided Bybit configuration:
```bash
cp config_bybit_futures.json config.json
```

Or manually configure:
```json
{
    "exchange": {
        "name": "bybit",
        "key": "your_bybit_api_key",
        "secret": "your_bybit_api_secret",
        "ccxt_config": {
            "enableRateLimit": true,
            "rateLimit": 120,
            "timeout": 30000,
            "options": {
                "defaultType": "linear"
            }
        },
        "pair_whitelist": [
            "BTC/USDT:USDT",
            "ETH/USDT:USDT",
            "SOL/USDT:USDT",
            "BNB/USDT:USDT",
            "ADA/USDT:USDT"
        ]
    },
    "trading_mode": "futures",
    "margin_mode": "isolated",
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 1500,
        "scale": 0.4
    }
}
```

### 3. Bybit-Optimized Strategy Parameters

For Bybit futures, use these optimized parameters:

```json
{
    "orderflow_strategy_params": {
        "delta_period": 15,
        "cvd_period": 40,
        "min_confluence_score": 58,
        "strong_signal_threshold": 78,
        "imbalance_threshold": 2.8,
        "volume_threshold": 1.3,
        "divergence_lookback": 18,
        "exhaustion_threshold": 0.75
    }
}
```

## Bybit-Specific Features

### Futures Trading Advantages

1. **High Leverage**: Up to 100x leverage (use responsibly)
2. **Low Fees**: 0.01% maker, 0.06% taker fees
3. **Deep Liquidity**: Excellent for orderflow analysis
4. **24/7 Trading**: Continuous market access
5. **USDT Settlement**: Direct USDT profits/losses

### Recommended Pairs for Orderflow

**High Volume Pairs** (best orderflow data):
- BTC/USDT:USDT
- ETH/USDT:USDT
- SOL/USDT:USDT

**Medium Volume Pairs**:
- BNB/USDT:USDT
- ADA/USDT:USDT
- MATIC/USDT:USDT
- DOT/USDT:USDT
- AVAX/USDT:USDT

**Avoid Low Volume Pairs**: Limited orderflow data quality

### Risk Management for Futures

```json
{
    "max_open_trades": 3,
    "stake_amount": 200,
    "stoploss": -0.03,
    "trailing_stop": true,
    "trailing_stop_positive": 0.006,
    "trailing_stop_positive_offset": 0.012,
    
    "protections": [
        {
            "method": "StoplossGuard",
            "lookback_period_candles": 60,
            "trade_limit": 3,
            "stop_duration_candles": 60
        },
        {
            "method": "MaxDrawdown",
            "lookback_period_candles": 200,
            "trade_limit": 15,
            "stop_duration_candles": 120,
            "max_allowed_drawdown": 0.15
        }
    ]
}
```

## Testing and Validation

### 1. Paper Trading Setup

**Always start with paper trading:**
```json
{
    "dry_run": true,
    "stake_amount": 100
}
```

### 2. Data Download for Backtesting

```bash
# Download data for Bybit futures
python run_backtest.py --download --timerange 20240101-20241201

# Note: Bybit may have rate limits on trades data
# If download fails, try smaller timeranges:
python run_backtest.py --download --timerange 20241001-20241201
```

### 3. Backtesting Commands

```bash
# Quick backtest
freqtrade backtesting --config config_bybit_futures.json --strategy OrderflowStrategy --timerange 20240101-20241201

# Detailed backtest with breakdown
freqtrade backtesting --config config_bybit_futures.json --strategy OrderflowStrategy --timerange 20240101-20241201 --breakdown day week month --export trades
```

## Bybit-Specific Considerations

### Rate Limits

Bybit has specific rate limits:
- **REST API**: 120 requests/minute
- **WebSocket**: 200 connections max
- **Trades Data**: May have additional limits

**Optimization for Rate Limits:**
```json
{
    "orderflow": {
        "cache_size": 1500,
        "scale": 0.4
    },
    "exchange": {
        "ccxt_config": {
            "rateLimit": 120,
            "enableRateLimit": true
        }
    }
}
```

### Margin Requirements

**Isolated Margin** (recommended):
- Each position has separate margin
- Limited risk per position
- Better risk management

**Cross Margin** (advanced):
- Shared margin across positions
- Higher capital efficiency
- Increased risk

### Position Sizing for Futures

```python
# Conservative sizing for futures
stake_amount = account_balance * 0.02  # 2% per trade
max_leverage = 5  # Conservative leverage

# Example for $10,000 account:
# stake_amount = $200 per trade
# With 5x leverage = $1,000 position size
```

## Live Trading Checklist

### Pre-Live Setup

- [ ] ✅ API keys configured with correct permissions
- [ ] ✅ Paper trading tested successfully
- [ ] ✅ Backtesting shows positive results
- [ ] ✅ Risk management parameters set
- [ ] ✅ Telegram notifications configured
- [ ] ✅ Position sizing appropriate for account

### Go-Live Process

1. **Start Small**: Begin with minimum position sizes
2. **Monitor Closely**: Watch first few trades carefully
3. **Gradual Scaling**: Increase position sizes gradually
4. **Performance Tracking**: Monitor key metrics daily

### Monitoring Dashboard

Key metrics to track:
- **Win Rate**: Target 55-65%
- **Profit Factor**: Target >1.3
- **Max Drawdown**: Keep <15%
- **Daily P&L**: Track consistency
- **Signal Quality**: Monitor confluence scores

## Troubleshooting

### Common Bybit Issues

**1. API Connection Errors**
```
Solution: Check API key permissions and IP restrictions
```

**2. Insufficient Margin**
```
Solution: Reduce position sizes or add more margin
```

**3. Rate Limit Exceeded**
```
Solution: Increase rateLimit in config, reduce cache_size
```

**4. No Orderflow Data**
```
Solution: Ensure use_public_trades: true, try different pairs
```

### Performance Optimization

```json
{
    "orderflow": {
        "cache_size": 1000,
        "scale": 0.3
    },
    "internals": {
        "process_throttle_secs": 5,
        "heartbeat_interval": 60
    }
}
```

## Support and Resources

### Bybit Resources
- [Bybit API Documentation](https://bybit-exchange.github.io/docs/)
- [Bybit Trading Fees](https://www.bybit.com/en-US/help-center/bybitHC_Article?language=en_US&id=000001044)
- [Bybit Risk Management](https://www.bybit.com/en-US/help-center/bybitHC_Article?language=en_US&id=000001046)

### Strategy Resources
- **USAGE_GUIDE.md**: Complete usage instructions
- **TECHNICAL_DOCS.md**: Technical implementation details
- **CONFIG_EXAMPLES.md**: Additional configuration examples

### Getting Help

1. Check troubleshooting section above
2. Validate setup with provided scripts
3. Review Bybit API documentation
4. Test with paper trading first

---

**⚠️ Important Disclaimer**: Futures trading involves significant risk. Always start with paper trading and small position sizes. Never risk more than you can afford to lose.
