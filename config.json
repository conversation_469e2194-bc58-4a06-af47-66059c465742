{"max_open_trades": 3, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 1000, "cancel_open_orders_on_exit": false, "trading_mode": "futures", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "AVAX/USDT:USDT", "MATIC/USDT:USDT"], "pair_blacklist": [".*_PREMIUM", ".*_DOWN", ".*_UP"], "use_public_trades": true}, "orderflow": {"cache_size": 1000, "max_candles": 1500, "scale": 0.5, "stacked_imbalance_range": 3, "imbalance_volume": 1, "imbalance_ratio": 3}, "pairlists": [{"method": "StaticPairList"}], "timeframe": "5m", "informative_pairs": [], "startup_candle_count": 400, "bot_name": "OrderflowBot", "force_entry_enable": false, "initial_state": "running", "internals": {"process_throttle_secs": 5}}