# Configuration Examples for OrderflowStrategy

## Table of Contents
1. [Basic Configuration](#basic-configuration)
2. [Exchange-Specific Configs](#exchange-specific-configs)
3. [Risk Management Profiles](#risk-management-profiles)
4. [Strategy Parameter Sets](#strategy-parameter-sets)
5. [Environment-Specific Configs](#environment-specific-configs)

## Basic Configuration

### Minimal Configuration
```json
{
    "max_open_trades": 3,
    "stake_currency": "USDT",
    "stake_amount": 1000,
    "tradable_balance_ratio": 0.99,
    "fiat_display_currency": "USD",
    "timeframe": "5m",
    "dry_run": true,
    "cancel_open_orders_on_exit": false,
    
    "exchange": {
        "name": "binance",
        "ccxt_config": {
            "enableRateLimit": true,
            "rateLimit": 200
        },
        "pair_whitelist": [
            "BTC/USDT",
            "ETH/USDT",
            "BNB/USDT"
        ],
        "pair_blacklist": []
    },
    
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 1000,
        "scale": 0.5
    },
    
    "strategy": "OrderflowStrategy",
    "strategy_path": "strategies/",
    
    "stoploss": -0.05,
    "trailing_stop": true,
    "trailing_stop_positive": 0.01,
    "trailing_stop_positive_offset": 0.02,
    "trailing_only_offset_is_reached": false
}
```

### Production Configuration
```json
{
    "max_open_trades": 5,
    "stake_currency": "USDT",
    "stake_amount": 500,
    "tradable_balance_ratio": 0.95,
    "fiat_display_currency": "USD",
    "timeframe": "5m",
    "dry_run": false,
    "cancel_open_orders_on_exit": true,
    
    "exchange": {
        "name": "binance",
        "key": "your_api_key",
        "secret": "your_api_secret",
        "ccxt_config": {
            "enableRateLimit": true,
            "rateLimit": 200,
            "timeout": 30000
        },
        "pair_whitelist": [
            "BTC/USDT", "ETH/USDT", "BNB/USDT",
            "ADA/USDT", "SOL/USDT", "MATIC/USDT",
            "DOT/USDT", "AVAX/USDT"
        ],
        "pair_blacklist": [
            ".*_BTC", ".*_ETH"
        ]
    },
    
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 2000,
        "scale": 0.3
    },
    
    "strategy": "OrderflowStrategy",
    "strategy_path": "strategies/",
    
    "stoploss": -0.03,
    "trailing_stop": true,
    "trailing_stop_positive": 0.005,
    "trailing_stop_positive_offset": 0.01,
    "trailing_only_offset_is_reached": true,
    
    "protections": [
        {
            "method": "StoplossGuard",
            "lookback_period_candles": 60,
            "trade_limit": 4,
            "stop_duration_candles": 60,
            "only_per_pair": false
        },
        {
            "method": "MaxDrawdown",
            "lookback_period_candles": 200,
            "trade_limit": 20,
            "stop_duration_candles": 100,
            "max_allowed_drawdown": 0.2
        },
        {
            "method": "LowProfitPairs",
            "lookback_period_candles": 1440,
            "trade_limit": 20,
            "stop_duration_candles": 60,
            "required_profit": 0.02
        }
    ],
    
    "telegram": {
        "enabled": true,
        "token": "your_telegram_bot_token",
        "chat_id": "your_chat_id",
        "notification_settings": {
            "status": "on",
            "warning": "on",
            "startup": "on",
            "buy": "on",
            "sell": "on",
            "buy_cancel": "on",
            "sell_cancel": "on",
            "buy_fill": "on",
            "sell_fill": "on",
            "protection_trigger": "on",
            "protection_trigger_global": "on"
        }
    }
}
```

## Exchange-Specific Configs

### Bybit Futures Configuration (Recommended)
```json
{
    "exchange": {
        "name": "bybit",
        "key": "your_bybit_api_key",
        "secret": "your_bybit_api_secret",
        "ccxt_config": {
            "enableRateLimit": true,
            "rateLimit": 120,
            "timeout": 30000,
            "options": {
                "defaultType": "linear"
            }
        },
        "pair_whitelist": [
            "BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT",
            "BNB/USDT:USDT", "ADA/USDT:USDT", "MATIC/USDT:USDT",
            "DOT/USDT:USDT", "AVAX/USDT:USDT", "LINK/USDT:USDT"
        ]
    },
    "trading_mode": "futures",
    "margin_mode": "isolated",
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 1500,
        "scale": 0.4
    }
}
```

### Bybit Spot Configuration
```json
{
    "exchange": {
        "name": "bybit",
        "key": "your_bybit_api_key",
        "secret": "your_bybit_api_secret",
        "ccxt_config": {
            "enableRateLimit": true,
            "rateLimit": 120,
            "timeout": 30000,
            "options": {
                "defaultType": "spot"
            }
        },
        "pair_whitelist": [
            "BTC/USDT", "ETH/USDT", "SOL/USDT",
            "BNB/USDT", "ADA/USDT", "MATIC/USDT"
        ]
    },
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 1200,
        "scale": 0.5
    }
}
```

### Binance Configuration
```json
{
    "exchange": {
        "name": "binance",
        "key": "your_api_key",
        "secret": "your_api_secret",
        "ccxt_config": {
            "enableRateLimit": true,
            "rateLimit": 200,
            "timeout": 30000,
            "options": {
                "defaultType": "spot"
            }
        },
        "pair_whitelist": [
            "BTC/USDT", "ETH/USDT", "BNB/USDT",
            "ADA/USDT", "SOL/USDT", "MATIC/USDT"
        ]
    },
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 2000,
        "scale": 0.5
    }
}
```

### Binance Futures Configuration
```json
{
    "exchange": {
        "name": "binance",
        "key": "your_futures_api_key",
        "secret": "your_futures_api_secret",
        "ccxt_config": {
            "enableRateLimit": true,
            "rateLimit": 200,
            "timeout": 30000,
            "options": {
                "defaultType": "future"
            }
        },
        "pair_whitelist": [
            "BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT"
        ]
    },
    "trading_mode": "futures",
    "margin_mode": "isolated",
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 1500,
        "scale": 0.3
    }
}
```

### Kraken Configuration
```json
{
    "exchange": {
        "name": "kraken",
        "key": "your_api_key",
        "secret": "your_api_secret",
        "ccxt_config": {
            "enableRateLimit": true,
            "rateLimit": 1000,
            "timeout": 60000
        },
        "pair_whitelist": [
            "BTC/USD", "ETH/USD", "ADA/USD"
        ]
    },
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 1000,
        "scale": 0.7
    }
}
```

## Risk Management Profiles

### Conservative Profile
```json
{
    "max_open_trades": 2,
    "stake_amount": 200,
    "stoploss": -0.02,
    "trailing_stop": true,
    "trailing_stop_positive": 0.005,
    "trailing_stop_positive_offset": 0.01,
    
    "protections": [
        {
            "method": "StoplossGuard",
            "lookback_period_candles": 120,
            "trade_limit": 2,
            "stop_duration_candles": 120
        },
        {
            "method": "MaxDrawdown",
            "lookback_period_candles": 200,
            "trade_limit": 10,
            "stop_duration_candles": 200,
            "max_allowed_drawdown": 0.1
        }
    ],
    
    "orderflow_strategy_params": {
        "min_confluence_score": 70,
        "strong_signal_threshold": 85,
        "volume_threshold": 2.0
    }
}
```

### Aggressive Profile
```json
{
    "max_open_trades": 8,
    "stake_amount": 1000,
    "stoploss": -0.08,
    "trailing_stop": true,
    "trailing_stop_positive": 0.02,
    "trailing_stop_positive_offset": 0.03,
    
    "protections": [
        {
            "method": "StoplossGuard",
            "lookback_period_candles": 30,
            "trade_limit": 6,
            "stop_duration_candles": 30
        },
        {
            "method": "MaxDrawdown",
            "lookback_period_candles": 100,
            "trade_limit": 30,
            "stop_duration_candles": 50,
            "max_allowed_drawdown": 0.3
        }
    ],
    
    "orderflow_strategy_params": {
        "min_confluence_score": 50,
        "strong_signal_threshold": 70,
        "volume_threshold": 1.2
    }
}
```

### Balanced Profile
```json
{
    "max_open_trades": 5,
    "stake_amount": 500,
    "stoploss": -0.05,
    "trailing_stop": true,
    "trailing_stop_positive": 0.01,
    "trailing_stop_positive_offset": 0.02,
    
    "protections": [
        {
            "method": "StoplossGuard",
            "lookback_period_candles": 60,
            "trade_limit": 4,
            "stop_duration_candles": 60
        },
        {
            "method": "MaxDrawdown",
            "lookback_period_candles": 200,
            "trade_limit": 20,
            "stop_duration_candles": 100,
            "max_allowed_drawdown": 0.2
        }
    ],
    
    "orderflow_strategy_params": {
        "min_confluence_score": 60,
        "strong_signal_threshold": 80,
        "volume_threshold": 1.5
    }
}
```

## Strategy Parameter Sets

### Scalping Parameters (1m-5m timeframes)
```json
{
    "orderflow_strategy_params": {
        "delta_period": 10,
        "cvd_period": 20,
        "min_confluence_score": 55,
        "strong_signal_threshold": 75,
        "imbalance_threshold": 2.5,
        "volume_threshold": 1.2,
        "divergence_lookback": 10,
        "exhaustion_threshold": 0.7
    }
}
```

### Swing Trading Parameters (15m-1h timeframes)
```json
{
    "orderflow_strategy_params": {
        "delta_period": 30,
        "cvd_period": 100,
        "min_confluence_score": 65,
        "strong_signal_threshold": 85,
        "imbalance_threshold": 3.5,
        "volume_threshold": 2.0,
        "divergence_lookback": 30,
        "exhaustion_threshold": 0.9
    }
}
```

### High-Frequency Parameters
```json
{
    "orderflow_strategy_params": {
        "delta_period": 5,
        "cvd_period": 15,
        "min_confluence_score": 50,
        "strong_signal_threshold": 70,
        "imbalance_threshold": 2.0,
        "volume_threshold": 1.0,
        "divergence_lookback": 5,
        "exhaustion_threshold": 0.6
    }
}
```

### Conservative Parameters
```json
{
    "orderflow_strategy_params": {
        "delta_period": 50,
        "cvd_period": 200,
        "min_confluence_score": 75,
        "strong_signal_threshold": 90,
        "imbalance_threshold": 4.0,
        "volume_threshold": 2.5,
        "divergence_lookback": 50,
        "exhaustion_threshold": 0.95
    }
}
```

## Environment-Specific Configs

### Development/Testing
```json
{
    "dry_run": true,
    "stake_amount": 100,
    "max_open_trades": 2,
    
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 500,
        "scale": 1.0
    },
    
    "logging": {
        "verbosity": 3,
        "logfile": "logs/freqtrade_dev.log"
    },
    
    "api_server": {
        "enabled": true,
        "listen_ip_address": "127.0.0.1",
        "listen_port": 8080,
        "verbosity": "info",
        "enable_openapi": true,
        "jwt_secret_key": "your_jwt_secret",
        "CORS_origins": ["http://localhost:3000"],
        "username": "freqtrade",
        "password": "your_password"
    }
}
```

### Production
```json
{
    "dry_run": false,
    "stake_amount": 500,
    "max_open_trades": 5,
    
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 2000,
        "scale": 0.3
    },
    
    "logging": {
        "verbosity": 2,
        "logfile": "logs/freqtrade_prod.log"
    },
    
    "api_server": {
        "enabled": true,
        "listen_ip_address": "0.0.0.0",
        "listen_port": 8080,
        "verbosity": "warning",
        "enable_openapi": false,
        "jwt_secret_key": "your_secure_jwt_secret",
        "username": "admin",
        "password": "your_secure_password"
    }
}
```

### Backtesting
```json
{
    "timerange": "20240101-20241201",
    "max_open_trades": 3,
    "stake_amount": 1000,
    
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 1000,
        "scale": 0.5
    },
    
    "protections": [
        {
            "method": "StoplossGuard",
            "lookback_period_candles": 60,
            "trade_limit": 4,
            "stop_duration_candles": 60
        }
    ],
    
    "backtest_breakdown": ["day", "week", "month"],
    "export": "trades",
    "exportfilename": "backtest_results/orderflow_strategy"
}
```

## Optimization Configurations

### Hyperopt Configuration
```json
{
    "hyperopt": {
        "hyperopt_loss": "SharpeHyperOptLoss",
        "hyperopt_random_state": 42,
        "hyperopt_min_trades": 10,
        "hyperopt_epochs": 100,
        "hyperopt_jobs": -1,
        "hyperopt_show_index": false,
        "hyperopt_list_best": 5,
        "hyperopt_list_profitable": true,
        "hyperopt_list_min_trades": 20,
        "hyperopt_list_max_trades": 0,
        "hyperopt_list_min_avg_time": 0,
        "hyperopt_list_max_avg_time": 0,
        "hyperopt_list_min_avg_profit": 0,
        "hyperopt_list_max_avg_profit": 0,
        "hyperopt_list_min_total_profit": 0,
        "hyperopt_list_max_total_profit": 0,
        "hyperopt_list_min_objective": 0,
        "hyperopt_list_max_objective": 0
    },
    
    "spaces": ["buy", "sell", "protection", "roi", "stoploss", "trailing"]
}
```

### Parameter Ranges for Optimization
```json
{
    "strategy_parameters": {
        "delta_period": {
            "type": "int",
            "min": 5,
            "max": 50,
            "default": 20
        },
        "cvd_period": {
            "type": "int", 
            "min": 10,
            "max": 200,
            "default": 50
        },
        "min_confluence_score": {
            "type": "int",
            "min": 30,
            "max": 90,
            "default": 60
        },
        "strong_signal_threshold": {
            "type": "int",
            "min": 60,
            "max": 95,
            "default": 80
        }
    }
}
```
