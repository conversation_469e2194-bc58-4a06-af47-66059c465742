#!/usr/bin/env python3
"""
Data validation script for OrderflowStrategy
Validates OHLCV and trades data quality for backtesting
"""

import os
import json
import pandas as pd
from pathlib import Path
from datetime import datetime
import gzip

class DataValidator:
    def __init__(self, config_file="config_backtest.json"):
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        
        self.data_dir = Path("user_data/data/binance")
        self.pairs = self.config['exchange']['pair_whitelist']
        self.timeframe = self.config['timeframe']
    
    def validate_ohlcv_data(self):
        """Validate OHLCV data completeness and quality"""
        print("🔍 Validating OHLCV data...")
        
        validation_results = {}
        
        for pair in self.pairs:
            pair_safe = pair.replace('/', '_')
            ohlcv_file = self.data_dir / f"{pair_safe}-{self.timeframe}.json"
            
            if not ohlcv_file.exists():
                validation_results[pair] = {
                    'status': 'missing',
                    'message': f'OHLCV file not found: {ohlcv_file}'
                }
                continue
            
            try:
                # Load OHLCV data
                with open(ohlcv_file, 'r') as f:
                    data = json.load(f)
                
                df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                
                # Validation checks
                issues = []
                
                # Check for missing data
                if len(df) == 0:
                    issues.append("No data found")
                
                # Check for gaps in data
                expected_interval = pd.Timedelta(minutes=5)  # 5m timeframe
                time_diffs = df['timestamp'].diff().dropna()
                gaps = time_diffs[time_diffs > expected_interval * 1.5]
                if len(gaps) > 0:
                    issues.append(f"Found {len(gaps)} time gaps")
                
                # Check for invalid OHLCV values
                if (df[['open', 'high', 'low', 'close', 'volume']] <= 0).any().any():
                    issues.append("Found zero or negative OHLCV values")
                
                # Check OHLC consistency
                ohlc_issues = (
                    (df['high'] < df['low']) |
                    (df['high'] < df['open']) |
                    (df['high'] < df['close']) |
                    (df['low'] > df['open']) |
                    (df['low'] > df['close'])
                )
                if ohlc_issues.any():
                    issues.append(f"Found {ohlc_issues.sum()} OHLC inconsistencies")
                
                # Check for extreme price movements (>50% in one candle)
                price_changes = df['close'].pct_change().abs()
                extreme_moves = price_changes > 0.5
                if extreme_moves.any():
                    issues.append(f"Found {extreme_moves.sum()} extreme price movements")
                
                validation_results[pair] = {
                    'status': 'valid' if not issues else 'issues',
                    'candles': len(df),
                    'date_range': f"{df['timestamp'].min()} to {df['timestamp'].max()}",
                    'issues': issues
                }
                
            except Exception as e:
                validation_results[pair] = {
                    'status': 'error',
                    'message': f'Error loading data: {str(e)}'
                }
        
        return validation_results
    
    def validate_trades_data(self):
        """Validate trades data for orderflow analysis"""
        print("🔍 Validating trades data...")
        
        validation_results = {}
        
        for pair in self.pairs:
            pair_safe = pair.replace('/', '_')
            trades_file = self.data_dir / f"{pair_safe}-trades.json.gz"
            
            if not trades_file.exists():
                validation_results[pair] = {
                    'status': 'missing',
                    'message': f'Trades file not found: {trades_file}'
                }
                continue
            
            try:
                # Load trades data
                with gzip.open(trades_file, 'rt') as f:
                    trades_data = []
                    for line in f:
                        trades_data.append(json.loads(line))
                
                if not trades_data:
                    validation_results[pair] = {
                        'status': 'empty',
                        'message': 'No trades data found'
                    }
                    continue
                
                df = pd.DataFrame(trades_data)
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                
                # Validation checks
                issues = []
                
                # Check required columns for orderflow
                required_cols = ['timestamp', 'price', 'amount', 'side']
                missing_cols = [col for col in required_cols if col not in df.columns]
                if missing_cols:
                    issues.append(f"Missing columns: {missing_cols}")
                
                # Check for valid side values
                if 'side' in df.columns:
                    invalid_sides = df[~df['side'].isin(['buy', 'sell'])]
                    if len(invalid_sides) > 0:
                        issues.append(f"Found {len(invalid_sides)} invalid side values")
                
                # Check for zero/negative prices or amounts
                if 'price' in df.columns and (df['price'] <= 0).any():
                    issues.append("Found zero or negative prices")
                
                if 'amount' in df.columns and (df['amount'] <= 0).any():
                    issues.append("Found zero or negative amounts")
                
                # Check data density (trades per hour)
                if len(df) > 0:
                    time_span = (df['timestamp'].max() - df['timestamp'].min()).total_seconds() / 3600
                    trades_per_hour = len(df) / max(time_span, 1)
                    
                    if trades_per_hour < 10:  # Very low trade density
                        issues.append(f"Low trade density: {trades_per_hour:.1f} trades/hour")
                
                validation_results[pair] = {
                    'status': 'valid' if not issues else 'issues',
                    'trades_count': len(df),
                    'date_range': f"{df['timestamp'].min()} to {df['timestamp'].max()}" if len(df) > 0 else "No data",
                    'trades_per_hour': trades_per_hour if len(df) > 0 else 0,
                    'issues': issues
                }
                
            except Exception as e:
                validation_results[pair] = {
                    'status': 'error',
                    'message': f'Error loading trades data: {str(e)}'
                }
        
        return validation_results
    
    def generate_report(self, ohlcv_results, trades_results):
        """Generate comprehensive validation report"""
        print("\n" + "="*60)
        print("📊 DATA VALIDATION REPORT")
        print("="*60)
        
        # OHLCV Summary
        print("\n🕯️  OHLCV DATA SUMMARY:")
        ohlcv_valid = sum(1 for r in ohlcv_results.values() if r['status'] == 'valid')
        ohlcv_issues = sum(1 for r in ohlcv_results.values() if r['status'] == 'issues')
        ohlcv_missing = sum(1 for r in ohlcv_results.values() if r['status'] == 'missing')
        
        print(f"  ✅ Valid: {ohlcv_valid}/{len(self.pairs)}")
        print(f"  ⚠️  Issues: {ohlcv_issues}/{len(self.pairs)}")
        print(f"  ❌ Missing: {ohlcv_missing}/{len(self.pairs)}")
        
        # Trades Summary
        print("\n📈 TRADES DATA SUMMARY:")
        trades_valid = sum(1 for r in trades_results.values() if r['status'] == 'valid')
        trades_issues = sum(1 for r in trades_results.values() if r['status'] == 'issues')
        trades_missing = sum(1 for r in trades_results.values() if r['status'] == 'missing')
        
        print(f"  ✅ Valid: {trades_valid}/{len(self.pairs)}")
        print(f"  ⚠️  Issues: {trades_issues}/{len(self.pairs)}")
        print(f"  ❌ Missing: {trades_missing}/{len(self.pairs)}")
        
        # Detailed results
        print("\n📋 DETAILED RESULTS:")
        for pair in self.pairs:
            print(f"\n{pair}:")
            
            # OHLCV status
            ohlcv = ohlcv_results.get(pair, {})
            status_icon = {"valid": "✅", "issues": "⚠️", "missing": "❌", "error": "💥"}.get(ohlcv.get('status'), "❓")
            print(f"  OHLCV: {status_icon} {ohlcv.get('status', 'unknown')}")
            
            if 'candles' in ohlcv:
                print(f"    Candles: {ohlcv['candles']}")
                print(f"    Range: {ohlcv['date_range']}")
            
            if 'issues' in ohlcv and ohlcv['issues']:
                for issue in ohlcv['issues']:
                    print(f"    ⚠️  {issue}")
            
            # Trades status
            trades = trades_results.get(pair, {})
            status_icon = {"valid": "✅", "issues": "⚠️", "missing": "❌", "error": "💥"}.get(trades.get('status'), "❓")
            print(f"  Trades: {status_icon} {trades.get('status', 'unknown')}")
            
            if 'trades_count' in trades:
                print(f"    Count: {trades['trades_count']}")
                print(f"    Density: {trades.get('trades_per_hour', 0):.1f} trades/hour")
            
            if 'issues' in trades and trades['issues']:
                for issue in trades['issues']:
                    print(f"    ⚠️  {issue}")
        
        # Recommendations
        print("\n💡 RECOMMENDATIONS:")
        
        if ohlcv_missing > 0 or trades_missing > 0:
            print("  • Run data download: python run_backtest.py --download")
        
        if ohlcv_issues > 0 or trades_issues > 0:
            print("  • Review data quality issues before backtesting")
            print("  • Consider using different timerange or pairs")
        
        if trades_missing == len(self.pairs):
            print("  • Orderflow analysis will be limited without trades data")
            print("  • Strategy will fall back to technical indicators only")
        
        print("\n" + "="*60)

def main():
    validator = DataValidator()
    
    # Validate data
    ohlcv_results = validator.validate_ohlcv_data()
    trades_results = validator.validate_trades_data()
    
    # Generate report
    validator.generate_report(ohlcv_results, trades_results)

if __name__ == "__main__":
    main()
