# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import numpy as np
import pandas as pd
from pandas import DataFrame, Series
from datetime import datetime, timedelta
from typing import Optional, Union

from freqtrade.strategy import IStrategy, merge_informative_pair, stoploss_from_open, DecimalParameter, IntParameter, CategoricalParameter
from freqtrade.persistence import Trade

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import pandas_ta as pta


class OrderflowStrategy(IStrategy):
    """
    Advanced Orderflow Trading Strategy for Freqtrade
    
    This strategy uses orderflow analysis including:
    - Delta analysis and Cumulative Volume Delta (CVD)
    - Imbalance detection and stacked imbalances  
    - Footprint chart analysis
    - Multi-indicator confluence scoring
    - Dynamic risk management based on orderflow signals
    
    Author: Freqtrade Orderflow Strategy
    Version: 1.0.0
    """

    # Strategy interface version - allow new iterations of the strategy interface.
    # Check the documentation or the Sample strategy to get the latest version.
    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy.
    timeframe = '5m'

    # Can this strategy go short?
    can_short: bool = True

    # Minimal ROI designed for the strategy.
    minimal_roi = {
        "0": 0.02,    # 2% profit target
        "30": 0.015,  # 1.5% after 30 minutes
        "60": 0.01,   # 1% after 1 hour
        "120": 0.005, # 0.5% after 2 hours
        "240": 0      # Break even after 4 hours
    }

    # Optimal stoploss designed for the strategy.
    stoploss = -0.05  # 5% stop loss

    # Trailing stoploss
    trailing_stop = True
    trailing_stop_positive = 0.01
    trailing_stop_positive_offset = 0.015
    trailing_only_offset_is_reached = True

    # Run "populate_indicators" only for new candle.
    process_only_new_candles = True

    # These values can be overridden in the config.
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 400

    # Strategy parameters
    # Delta analysis parameters
    delta_period = IntParameter(5, 20, default=10, space="buy")
    delta_threshold = DecimalParameter(1.5, 3.0, default=2.0, space="buy")
    
    # CVD parameters  
    cvd_period = IntParameter(10, 30, default=20, space="buy")
    cvd_divergence_period = IntParameter(3, 10, default=5, space="buy")
    
    # Confluence scoring parameters
    min_confluence_score = IntParameter(60, 90, default=75, space="buy")
    strong_signal_threshold = IntParameter(80, 100, default=85, space="buy")

    # Position sizing parameters
    base_position_size = DecimalParameter(0.5, 2.0, default=1.0, space="buy")
    max_position_multiplier = DecimalParameter(1.2, 2.0, default=1.5, space="buy")

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        These pairs will automatically be available in strategy methods.
        """
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several different TA indicators to the given DataFrame
        
        Performance Note: For full feature set, this method uses 3 calls.
        - Request OHLCV data for primary timeframe
        - Request orderflow data (delta, CVD, imbalances, footprint)
        - Calculate technical indicators and confluence scores
        """
        
        # Ensure we have orderflow data available
        if 'delta' not in dataframe.columns:
            self.logger.warning("Orderflow data not available. Please enable 'use_public_trades' in config.")
            return dataframe
            
        # Initialize orderflow analysis
        dataframe = self._calculate_delta_indicators(dataframe)
        dataframe = self._calculate_cvd_indicators(dataframe)
        dataframe = self._calculate_imbalance_indicators(dataframe)
        dataframe = self._calculate_footprint_indicators(dataframe)
        dataframe = self._calculate_confluence_score(dataframe)
        
        # Add traditional technical indicators for confirmation
        dataframe = self._add_technical_indicators(dataframe)
        
        return dataframe

    def _calculate_delta_indicators(self, dataframe: DataFrame) -> DataFrame:
        """Calculate comprehensive delta-based indicators"""

        # Basic delta analysis
        dataframe['delta_ma'] = ta.SMA(dataframe['delta'], timeperiod=self.delta_period.value)
        dataframe['delta_ema'] = ta.EMA(dataframe['delta'], timeperiod=self.delta_period.value)
        dataframe['delta_std'] = dataframe['delta'].rolling(window=20).std()
        dataframe['delta_strong'] = abs(dataframe['delta']) > (dataframe['delta_std'] * self.delta_threshold.value)

        # Delta momentum and acceleration
        dataframe['delta_momentum'] = dataframe['delta'] - dataframe['delta'].shift(3)
        dataframe['delta_acceleration'] = dataframe['delta_momentum'] - dataframe['delta_momentum'].shift(1)
        dataframe['delta_velocity'] = dataframe['delta'] - dataframe['delta'].shift(1)

        # Delta trend analysis
        dataframe['delta_trend_up'] = (dataframe['delta_ma'] > dataframe['delta_ma'].shift(1)) & \
                                     (dataframe['delta_ma'].shift(1) > dataframe['delta_ma'].shift(2))
        dataframe['delta_trend_down'] = (dataframe['delta_ma'] < dataframe['delta_ma'].shift(1)) & \
                                       (dataframe['delta_ma'].shift(1) < dataframe['delta_ma'].shift(2))

        # Delta strength classification
        dataframe['delta_percentile'] = dataframe['delta'].rolling(window=100).rank(pct=True)
        dataframe['delta_extreme_positive'] = dataframe['delta_percentile'] > 0.95
        dataframe['delta_extreme_negative'] = dataframe['delta_percentile'] < 0.05

        # Delta oscillator (normalized delta)
        delta_max = dataframe['delta'].rolling(window=50).max()
        delta_min = dataframe['delta'].rolling(window=50).min()
        dataframe['delta_oscillator'] = (dataframe['delta'] - delta_min) / (delta_max - delta_min + 1e-8) * 100

        # Delta divergence with price
        dataframe['delta_price_divergence'] = self._calculate_delta_price_divergence(dataframe)

        # Delta volume relationship
        dataframe['delta_volume_ratio'] = dataframe['delta'] / (dataframe['volume'] + 1e-8)
        dataframe['delta_volume_efficiency'] = abs(dataframe['delta']) / (dataframe['volume'] + 1e-8)

        # Delta consistency (how often delta aligns with price movement)
        price_direction = (dataframe['close'] - dataframe['open']).apply(lambda x: 1 if x > 0 else -1 if x < 0 else 0)
        delta_direction = dataframe['delta'].apply(lambda x: 1 if x > 0 else -1 if x < 0 else 0)
        dataframe['delta_price_alignment'] = (price_direction == delta_direction).astype(int)
        dataframe['delta_consistency'] = dataframe['delta_price_alignment'].rolling(window=20).mean()

        # Delta exhaustion signals
        dataframe['delta_exhaustion_bull'] = (
            (dataframe['delta'] > 0) &
            (dataframe['delta'] < dataframe['delta'].shift(1)) &
            (dataframe['delta'].shift(1) < dataframe['delta'].shift(2)) &
            (dataframe['delta_percentile'] > 0.8)
        )

        dataframe['delta_exhaustion_bear'] = (
            (dataframe['delta'] < 0) &
            (dataframe['delta'] > dataframe['delta'].shift(1)) &
            (dataframe['delta'].shift(1) > dataframe['delta'].shift(2)) &
            (dataframe['delta_percentile'] < 0.2)
        )

        # Delta regime detection (trending vs ranging)
        dataframe['delta_regime'] = self._detect_delta_regime(dataframe)

        return dataframe

    def _calculate_delta_price_divergence(self, dataframe: DataFrame) -> DataFrame:
        """Calculate delta-price divergence signals"""
        # Look for divergences over different periods
        periods = [5, 10, 20]
        divergence_signals = []

        for period in periods:
            # Price peaks and troughs
            price_peaks = (dataframe['high'] == dataframe['high'].rolling(window=period*2+1, center=True).max())
            price_troughs = (dataframe['low'] == dataframe['low'].rolling(window=period*2+1, center=True).min())

            # Delta peaks and troughs
            delta_peaks = (dataframe['delta'] == dataframe['delta'].rolling(window=period*2+1, center=True).max())
            delta_troughs = (dataframe['delta'] == dataframe['delta'].rolling(window=period*2+1, center=True).min())

            # Bullish divergence: price makes lower low, delta makes higher low
            bullish_div = price_troughs & ~delta_troughs & (dataframe['delta'] > dataframe['delta'].shift(period))

            # Bearish divergence: price makes higher high, delta makes lower high
            bearish_div = price_peaks & ~delta_peaks & (dataframe['delta'] < dataframe['delta'].shift(period))

            divergence_signals.append(bullish_div.astype(int) - bearish_div.astype(int))

        # Combine divergence signals
        return pd.concat(divergence_signals, axis=1).sum(axis=1)

    def _detect_delta_regime(self, dataframe: DataFrame) -> DataFrame:
        """Detect if market is in trending or ranging regime based on delta"""
        # Calculate delta volatility
        delta_volatility = dataframe['delta'].rolling(window=20).std()
        delta_vol_ma = delta_volatility.rolling(window=10).mean()

        # Calculate delta trend strength
        delta_trend_strength = abs(dataframe['delta_ma'] - dataframe['delta_ma'].shift(10))

        # Regime classification
        regime = pd.Series('ranging', index=dataframe.index)

        # Trending regime: high trend strength and moderate volatility
        trending_condition = (
            (delta_trend_strength > delta_trend_strength.rolling(window=50).quantile(0.7)) &
            (delta_volatility > delta_vol_ma * 0.8) &
            (delta_volatility < delta_vol_ma * 2.0)
        )

        # Volatile regime: very high volatility
        volatile_condition = delta_volatility > delta_vol_ma * 2.0

        regime.loc[trending_condition] = 'trending'
        regime.loc[volatile_condition] = 'volatile'

        return regime

    def _calculate_cvd_indicators(self, dataframe: DataFrame) -> DataFrame:
        """Calculate comprehensive Cumulative Volume Delta indicators"""

        # Basic CVD calculation
        dataframe['cvd'] = dataframe['delta'].cumsum()
        dataframe['cvd_sma'] = ta.SMA(dataframe['cvd'], timeperiod=self.cvd_period.value)
        dataframe['cvd_ema'] = ta.EMA(dataframe['cvd'], timeperiod=self.cvd_period.value)
        dataframe['cvd_slope'] = (dataframe['cvd'] - dataframe['cvd'].shift(self.cvd_divergence_period.value)) / self.cvd_divergence_period.value

        # CVD trend analysis
        dataframe['cvd_trend_up'] = dataframe['cvd'] > dataframe['cvd_sma']
        dataframe['cvd_trend_down'] = dataframe['cvd'] < dataframe['cvd_sma']
        dataframe['cvd_trend_strength'] = abs(dataframe['cvd_slope'])

        # CVD momentum indicators
        dataframe['cvd_momentum'] = dataframe['cvd'] - dataframe['cvd'].shift(5)
        dataframe['cvd_acceleration'] = dataframe['cvd_momentum'] - dataframe['cvd_momentum'].shift(1)
        dataframe['cvd_velocity'] = dataframe['cvd'] - dataframe['cvd'].shift(1)

        # CVD oscillator (normalized CVD)
        cvd_max = dataframe['cvd'].rolling(window=100).max()
        cvd_min = dataframe['cvd'].rolling(window=100).min()
        dataframe['cvd_oscillator'] = (dataframe['cvd'] - cvd_min) / (cvd_max - cvd_min + 1e-8) * 100

        # CVD rate of change
        dataframe['cvd_roc'] = ((dataframe['cvd'] - dataframe['cvd'].shift(10)) / dataframe['cvd'].shift(10)) * 100

        # CVD divergence detection (enhanced)
        dataframe = self._calculate_cvd_divergences(dataframe)

        # CVD support and resistance levels
        dataframe = self._calculate_cvd_levels(dataframe)

        # CVD regime analysis
        dataframe['cvd_regime'] = self._detect_cvd_regime(dataframe)

        # CVD exhaustion signals
        dataframe['cvd_exhaustion_bull'] = (
            (dataframe['cvd_slope'] > 0) &
            (dataframe['cvd_slope'] < dataframe['cvd_slope'].shift(1)) &
            (dataframe['cvd_slope'].shift(1) < dataframe['cvd_slope'].shift(2)) &
            (dataframe['cvd_oscillator'] > 80)
        )

        dataframe['cvd_exhaustion_bear'] = (
            (dataframe['cvd_slope'] < 0) &
            (dataframe['cvd_slope'] > dataframe['cvd_slope'].shift(1)) &
            (dataframe['cvd_slope'].shift(1) > dataframe['cvd_slope'].shift(2)) &
            (dataframe['cvd_oscillator'] < 20)
        )

        # CVD breakout signals
        dataframe['cvd_breakout_bull'] = (
            (dataframe['cvd'] > dataframe['cvd'].rolling(window=20).max().shift(1)) &
            (dataframe['cvd_momentum'] > 0) &
            (dataframe['volume'] > dataframe['volume'].rolling(window=20).mean() * 1.2)
        )

        dataframe['cvd_breakout_bear'] = (
            (dataframe['cvd'] < dataframe['cvd'].rolling(window=20).min().shift(1)) &
            (dataframe['cvd_momentum'] < 0) &
            (dataframe['volume'] > dataframe['volume'].rolling(window=20).mean() * 1.2)
        )

        return dataframe

    def _calculate_cvd_divergences(self, dataframe: DataFrame) -> DataFrame:
        """Calculate sophisticated CVD divergence signals"""

        # Multiple timeframe divergence detection
        periods = [5, 10, 20, 50]

        for period in periods:
            # Price peaks and troughs detection
            price_peaks = dataframe['high'].rolling(window=period*2+1, center=True).max() == dataframe['high']
            price_troughs = dataframe['low'].rolling(window=period*2+1, center=True).min() == dataframe['low']

            # CVD peaks and troughs detection
            cvd_peaks = dataframe['cvd'].rolling(window=period*2+1, center=True).max() == dataframe['cvd']
            cvd_troughs = dataframe['cvd'].rolling(window=period*2+1, center=True).min() == dataframe['cvd']

            # Regular divergences
            dataframe[f'bearish_divergence_{period}'] = (
                price_peaks &
                (dataframe['high'] > dataframe['high'].shift(period)) &
                cvd_peaks &
                (dataframe['cvd'] < dataframe['cvd'].shift(period))
            )

            dataframe[f'bullish_divergence_{period}'] = (
                price_troughs &
                (dataframe['low'] < dataframe['low'].shift(period)) &
                cvd_troughs &
                (dataframe['cvd'] > dataframe['cvd'].shift(period))
            )

            # Hidden divergences
            dataframe[f'hidden_bearish_divergence_{period}'] = (
                price_peaks &
                (dataframe['high'] < dataframe['high'].shift(period)) &
                cvd_peaks &
                (dataframe['cvd'] > dataframe['cvd'].shift(period))
            )

            dataframe[f'hidden_bullish_divergence_{period}'] = (
                price_troughs &
                (dataframe['low'] > dataframe['low'].shift(period)) &
                cvd_troughs &
                (dataframe['cvd'] < dataframe['cvd'].shift(period))
            )

        # Aggregate divergence signals
        bearish_div_cols = [col for col in dataframe.columns if 'bearish_divergence_' in col]
        bullish_div_cols = [col for col in dataframe.columns if 'bullish_divergence_' in col]
        hidden_bearish_div_cols = [col for col in dataframe.columns if 'hidden_bearish_divergence_' in col]
        hidden_bullish_div_cols = [col for col in dataframe.columns if 'hidden_bullish_divergence_' in col]

        dataframe['bearish_divergence'] = dataframe[bearish_div_cols].any(axis=1)
        dataframe['bullish_divergence'] = dataframe[bullish_div_cols].any(axis=1)
        dataframe['hidden_bearish_divergence'] = dataframe[hidden_bearish_div_cols].any(axis=1)
        dataframe['hidden_bullish_divergence'] = dataframe[hidden_bullish_div_cols].any(axis=1)

        # Divergence strength (how many timeframes show divergence)
        dataframe['bearish_divergence_strength'] = dataframe[bearish_div_cols].sum(axis=1)
        dataframe['bullish_divergence_strength'] = dataframe[bullish_div_cols].sum(axis=1)

        return dataframe

    def _calculate_cvd_levels(self, dataframe: DataFrame) -> DataFrame:
        """Calculate CVD support and resistance levels"""

        # CVD pivot points
        window = 20
        dataframe['cvd_pivot_high'] = dataframe['cvd'].rolling(window=window*2+1, center=True).max() == dataframe['cvd']
        dataframe['cvd_pivot_low'] = dataframe['cvd'].rolling(window=window*2+1, center=True).min() == dataframe['cvd']

        # CVD support and resistance levels
        cvd_highs = dataframe.loc[dataframe['cvd_pivot_high'], 'cvd']
        cvd_lows = dataframe.loc[dataframe['cvd_pivot_low'], 'cvd']

        # Current CVD resistance (nearest high above current CVD)
        current_cvd = dataframe['cvd'].iloc[-1] if len(dataframe) > 0 else 0
        resistance_levels = cvd_highs[cvd_highs > current_cvd].sort_values()
        support_levels = cvd_lows[cvd_lows < current_cvd].sort_values(ascending=False)

        dataframe['cvd_resistance'] = resistance_levels.iloc[0] if len(resistance_levels) > 0 else None
        dataframe['cvd_support'] = support_levels.iloc[0] if len(support_levels) > 0 else None

        # Distance to key levels
        dataframe['cvd_resistance_distance'] = (dataframe['cvd_resistance'] - dataframe['cvd']) / abs(dataframe['cvd'] + 1e-8)
        dataframe['cvd_support_distance'] = (dataframe['cvd'] - dataframe['cvd_support']) / abs(dataframe['cvd'] + 1e-8)

        return dataframe

    def _detect_cvd_regime(self, dataframe: DataFrame) -> DataFrame:
        """Detect CVD regime (accumulation, distribution, neutral)"""

        # CVD trend over different periods
        cvd_trend_short = dataframe['cvd'] - dataframe['cvd'].shift(10)
        cvd_trend_medium = dataframe['cvd'] - dataframe['cvd'].shift(50)
        cvd_trend_long = dataframe['cvd'] - dataframe['cvd'].shift(100)

        # Price trend over same periods
        price_trend_short = dataframe['close'] - dataframe['close'].shift(10)
        price_trend_medium = dataframe['close'] - dataframe['close'].shift(50)
        price_trend_long = dataframe['close'] - dataframe['close'].shift(100)

        # Regime classification
        regime = pd.Series('neutral', index=dataframe.index)

        # Accumulation: CVD rising faster than price
        accumulation_condition = (
            (cvd_trend_short > 0) & (cvd_trend_medium > 0) &
            (cvd_trend_short > price_trend_short * 0.1) &
            (dataframe['cvd_slope'] > 0)
        )

        # Distribution: CVD falling while price stable/rising
        distribution_condition = (
            (cvd_trend_short < 0) & (cvd_trend_medium < 0) &
            (price_trend_short >= 0) &
            (dataframe['cvd_slope'] < 0)
        )

        # Strong accumulation: All timeframes aligned
        strong_accumulation_condition = (
            accumulation_condition &
            (cvd_trend_long > 0) &
            (dataframe['cvd_momentum'] > 0)
        )

        # Strong distribution: All timeframes aligned
        strong_distribution_condition = (
            distribution_condition &
            (cvd_trend_long < 0) &
            (dataframe['cvd_momentum'] < 0)
        )

        regime.loc[accumulation_condition] = 'accumulation'
        regime.loc[distribution_condition] = 'distribution'
        regime.loc[strong_accumulation_condition] = 'strong_accumulation'
        regime.loc[strong_distribution_condition] = 'strong_distribution'

        return regime

    def _calculate_imbalance_indicators(self, dataframe: DataFrame) -> DataFrame:
        """Calculate comprehensive imbalance-based indicators"""

        # Basic stacked imbalance analysis
        dataframe['strong_bid_stack'] = dataframe['stacked_imbalances_bid'].apply(lambda x: len(x) >= 3 if isinstance(x, list) else False)
        dataframe['strong_ask_stack'] = dataframe['stacked_imbalances_ask'].apply(lambda x: len(x) >= 3 if isinstance(x, list) else False)

        # Imbalance strength and counts
        dataframe['bid_imbalance_count'] = dataframe['stacked_imbalances_bid'].apply(lambda x: len(x) if isinstance(x, list) else 0)
        dataframe['ask_imbalance_count'] = dataframe['stacked_imbalances_ask'].apply(lambda x: len(x) if isinstance(x, list) else 0)

        # Enhanced imbalance analysis
        dataframe = self._analyze_imbalance_patterns(dataframe)
        dataframe = self._calculate_imbalance_strength(dataframe)
        dataframe = self._detect_imbalance_clusters(dataframe)
        dataframe = self._calculate_imbalance_momentum(dataframe)

        # Imbalance-based signals
        dataframe['imbalance_bullish_signal'] = (
            (dataframe['strong_bid_stack'] == True) &
            (dataframe['bid_imbalance_strength'] > 0.7) &
            (dataframe['imbalance_momentum_bull'] == True) &
            (dataframe['bid_imbalance_count'] >= 3)
        )

        dataframe['imbalance_bearish_signal'] = (
            (dataframe['strong_ask_stack'] == True) &
            (dataframe['ask_imbalance_strength'] > 0.7) &
            (dataframe['imbalance_momentum_bear'] == True) &
            (dataframe['ask_imbalance_count'] >= 3)
        )

        # Imbalance exhaustion signals
        dataframe['imbalance_exhaustion_bull'] = (
            (dataframe['bid_imbalance_count'] > 5) &
            (dataframe['bid_imbalance_strength'] > 0.9) &
            (dataframe['close'] > dataframe['high'].shift(1))
        )

        dataframe['imbalance_exhaustion_bear'] = (
            (dataframe['ask_imbalance_count'] > 5) &
            (dataframe['ask_imbalance_strength'] > 0.9) &
            (dataframe['close'] < dataframe['low'].shift(1))
        )

        return dataframe

    def _analyze_imbalance_patterns(self, dataframe: DataFrame) -> DataFrame:
        """Analyze imbalance patterns from footprint data"""

        # Extract imbalance information from footprint
        dataframe['footprint_imbalances'] = dataframe['orderflow'].apply(self._extract_footprint_imbalances)

        # Imbalance ratio analysis
        dataframe['max_bid_imbalance_ratio'] = dataframe['footprint_imbalances'].apply(lambda x: x.get('max_bid_ratio', 0))
        dataframe['max_ask_imbalance_ratio'] = dataframe['footprint_imbalances'].apply(lambda x: x.get('max_ask_ratio', 0))
        dataframe['avg_imbalance_ratio'] = dataframe['footprint_imbalances'].apply(lambda x: x.get('avg_ratio', 0))

        # Imbalance price levels
        dataframe['bid_imbalance_levels'] = dataframe['footprint_imbalances'].apply(lambda x: x.get('bid_levels', []))
        dataframe['ask_imbalance_levels'] = dataframe['footprint_imbalances'].apply(lambda x: x.get('ask_levels', []))

        # Imbalance coverage (what percentage of price range has imbalances)
        dataframe['imbalance_coverage'] = dataframe['footprint_imbalances'].apply(lambda x: x.get('coverage', 0))

        return dataframe

    def _extract_footprint_imbalances(self, orderflow_dict):
        """Extract detailed imbalance information from footprint data"""
        if not orderflow_dict or not isinstance(orderflow_dict, dict):
            return {'max_bid_ratio': 0, 'max_ask_ratio': 0, 'avg_ratio': 0, 'bid_levels': [], 'ask_levels': [], 'coverage': 0}

        bid_ratios = []
        ask_ratios = []
        bid_levels = []
        ask_levels = []
        total_levels = len(orderflow_dict)
        imbalanced_levels = 0

        for price, data in orderflow_dict.items():
            if not isinstance(data, dict):
                continue

            bid_amount = data.get('bid_amount', 0)
            ask_amount = data.get('ask_amount', 0)

            if bid_amount > 0 and ask_amount > 0:
                ratio = max(bid_amount, ask_amount) / min(bid_amount, ask_amount)

                # Significant imbalance threshold
                if ratio > 3:
                    imbalanced_levels += 1

                    if bid_amount > ask_amount:
                        bid_ratios.append(ratio)
                        bid_levels.append(float(price))
                    else:
                        ask_ratios.append(ratio)
                        ask_levels.append(float(price))

        coverage = imbalanced_levels / total_levels if total_levels > 0 else 0

        return {
            'max_bid_ratio': max(bid_ratios) if bid_ratios else 0,
            'max_ask_ratio': max(ask_ratios) if ask_ratios else 0,
            'avg_ratio': np.mean(bid_ratios + ask_ratios) if (bid_ratios or ask_ratios) else 0,
            'bid_levels': bid_levels,
            'ask_levels': ask_levels,
            'coverage': coverage
        }

    def _calculate_imbalance_strength(self, dataframe: DataFrame) -> DataFrame:
        """Calculate imbalance strength indicators"""

        # Normalize imbalance counts to strength scores (0-1)
        max_bid_count = dataframe['bid_imbalance_count'].rolling(window=100).max()
        max_ask_count = dataframe['ask_imbalance_count'].rolling(window=100).max()

        dataframe['bid_imbalance_strength'] = dataframe['bid_imbalance_count'] / (max_bid_count + 1e-8)
        dataframe['ask_imbalance_strength'] = dataframe['ask_imbalance_count'] / (max_ask_count + 1e-8)

        # Combined imbalance strength
        dataframe['total_imbalance_strength'] = dataframe['bid_imbalance_strength'] + dataframe['ask_imbalance_strength']

        # Imbalance dominance (which side is stronger)
        dataframe['imbalance_dominance'] = (dataframe['bid_imbalance_strength'] - dataframe['ask_imbalance_strength'])

        return dataframe

    def _detect_imbalance_clusters(self, dataframe: DataFrame) -> DataFrame:
        """Detect clusters of imbalances at similar price levels"""

        # Analyze imbalance clustering
        dataframe['bid_cluster_strength'] = dataframe['bid_imbalance_levels'].apply(self._calculate_cluster_strength)
        dataframe['ask_cluster_strength'] = dataframe['ask_imbalance_levels'].apply(self._calculate_cluster_strength)

        # Imbalance zone analysis
        dataframe['imbalance_zone_width'] = dataframe.apply(self._calculate_imbalance_zone_width, axis=1)
        dataframe['imbalance_zone_density'] = dataframe.apply(self._calculate_imbalance_zone_density, axis=1)

        return dataframe

    def _calculate_cluster_strength(self, levels):
        """Calculate the strength of imbalance clustering"""
        if not levels or len(levels) < 2:
            return 0

        levels = sorted(levels)
        distances = [levels[i+1] - levels[i] for i in range(len(levels)-1)]

        # Cluster strength based on proximity of levels
        avg_distance = np.mean(distances) if distances else 0
        min_distance = min(distances) if distances else 0

        # Strong cluster if levels are close together
        cluster_strength = 1 / (avg_distance + 1e-8) if avg_distance > 0 else 0

        return min(cluster_strength, 1.0)  # Cap at 1.0

    def _calculate_imbalance_zone_width(self, row):
        """Calculate the width of the imbalance zone"""
        bid_levels = row['bid_imbalance_levels']
        ask_levels = row['ask_imbalance_levels']

        all_levels = bid_levels + ask_levels
        if len(all_levels) < 2:
            return 0

        return max(all_levels) - min(all_levels)

    def _calculate_imbalance_zone_density(self, row):
        """Calculate the density of imbalances in the zone"""
        zone_width = row['imbalance_zone_width']
        total_imbalances = row['bid_imbalance_count'] + row['ask_imbalance_count']

        if zone_width <= 0:
            return 0

        return total_imbalances / zone_width

    def _calculate_imbalance_momentum(self, dataframe: DataFrame) -> DataFrame:
        """Calculate imbalance momentum indicators"""

        # Imbalance momentum (change in imbalance strength over time)
        dataframe['bid_imbalance_momentum'] = dataframe['bid_imbalance_strength'] - dataframe['bid_imbalance_strength'].shift(3)
        dataframe['ask_imbalance_momentum'] = dataframe['ask_imbalance_strength'] - dataframe['ask_imbalance_strength'].shift(3)

        # Imbalance acceleration
        dataframe['bid_imbalance_acceleration'] = dataframe['bid_imbalance_momentum'] - dataframe['bid_imbalance_momentum'].shift(1)
        dataframe['ask_imbalance_acceleration'] = dataframe['ask_imbalance_momentum'] - dataframe['ask_imbalance_momentum'].shift(1)

        # Momentum signals
        dataframe['imbalance_momentum_bull'] = (
            (dataframe['bid_imbalance_momentum'] > 0) &
            (dataframe['bid_imbalance_acceleration'] > 0) &
            (dataframe['ask_imbalance_momentum'] <= 0)
        )

        dataframe['imbalance_momentum_bear'] = (
            (dataframe['ask_imbalance_momentum'] > 0) &
            (dataframe['ask_imbalance_acceleration'] > 0) &
            (dataframe['bid_imbalance_momentum'] <= 0)
        )

        # Imbalance persistence (how long imbalances last)
        dataframe['bid_imbalance_persistence'] = self._calculate_imbalance_persistence(dataframe['strong_bid_stack'])
        dataframe['ask_imbalance_persistence'] = self._calculate_imbalance_persistence(dataframe['strong_ask_stack'])

        return dataframe

    def _calculate_imbalance_persistence(self, imbalance_series):
        """Calculate how long imbalances persist"""
        persistence = pd.Series(0, index=imbalance_series.index)
        current_streak = 0

        for i, has_imbalance in enumerate(imbalance_series):
            if has_imbalance:
                current_streak += 1
            else:
                current_streak = 0
            persistence.iloc[i] = current_streak

        return persistence

    def _calculate_footprint_indicators(self, dataframe: DataFrame) -> DataFrame:
        """Calculate comprehensive footprint chart indicators"""

        # Enhanced footprint analysis
        dataframe['footprint_analysis'] = dataframe['orderflow'].apply(self._analyze_footprint_comprehensive)

        # Basic footprint metrics
        dataframe['dominant_price_level'] = dataframe['footprint_analysis'].apply(lambda x: x.get('dominant_level') if x else None)
        dataframe['max_imbalance_ratio'] = dataframe['footprint_analysis'].apply(lambda x: x.get('max_ratio', 0) if x else 0)
        dataframe['total_footprint_volume'] = dataframe['footprint_analysis'].apply(lambda x: x.get('total_volume', 0) if x else 0)

        # Advanced footprint metrics
        dataframe['volume_weighted_price'] = dataframe['footprint_analysis'].apply(lambda x: x.get('vwap', 0) if x else 0)
        dataframe['footprint_skew'] = dataframe['footprint_analysis'].apply(lambda x: x.get('skew', 0) if x else 0)
        dataframe['footprint_kurtosis'] = dataframe['footprint_analysis'].apply(lambda x: x.get('kurtosis', 0) if x else 0)
        dataframe['price_level_count'] = dataframe['footprint_analysis'].apply(lambda x: x.get('level_count', 0) if x else 0)

        # Volume distribution analysis
        dataframe['volume_concentration'] = dataframe['footprint_analysis'].apply(lambda x: x.get('concentration', 0) if x else 0)
        dataframe['volume_dispersion'] = dataframe['footprint_analysis'].apply(lambda x: x.get('dispersion', 0) if x else 0)

        # Footprint patterns
        dataframe = self._detect_footprint_patterns(dataframe)

        # Volume profile analysis
        dataframe = self._analyze_volume_profile(dataframe)

        # Point of control analysis
        dataframe = self._analyze_point_of_control(dataframe)

        return dataframe

    def _analyze_footprint_comprehensive(self, orderflow_dict):
        """Comprehensive analysis of footprint data"""
        if not orderflow_dict or not isinstance(orderflow_dict, dict):
            return {
                'dominant_level': None, 'max_ratio': 0, 'total_volume': 0,
                'vwap': 0, 'skew': 0, 'kurtosis': 0, 'level_count': 0,
                'concentration': 0, 'dispersion': 0
            }

        prices = []
        volumes = []
        bid_volumes = []
        ask_volumes = []
        max_volume = 0
        dominant_level = None
        max_imbalance_ratio = 0
        total_volume = 0

        for price_str, data in orderflow_dict.items():
            if not isinstance(data, dict):
                continue

            price = float(price_str)
            vol = data.get('total_volume', 0)
            bid_amount = data.get('bid_amount', 0)
            ask_amount = data.get('ask_amount', 0)

            prices.append(price)
            volumes.append(vol)
            bid_volumes.append(bid_amount)
            ask_volumes.append(ask_amount)
            total_volume += vol

            if vol > max_volume:
                max_volume = vol
                dominant_level = price

            # Calculate imbalance ratio
            if bid_amount > 0 and ask_amount > 0:
                ratio = max(ask_amount, bid_amount) / min(ask_amount, bid_amount)
                max_imbalance_ratio = max(max_imbalance_ratio, ratio)

        if not prices or total_volume == 0:
            return {
                'dominant_level': None, 'max_ratio': 0, 'total_volume': 0,
                'vwap': 0, 'skew': 0, 'kurtosis': 0, 'level_count': 0,
                'concentration': 0, 'dispersion': 0
            }

        # Calculate VWAP
        vwap = sum(p * v for p, v in zip(prices, volumes)) / total_volume

        # Calculate volume distribution statistics
        volume_weights = [v / total_volume for v in volumes]

        # Skewness and kurtosis of volume distribution
        mean_price = sum(p * w for p, w in zip(prices, volume_weights))
        variance = sum(w * (p - mean_price) ** 2 for p, w in zip(prices, volume_weights))
        std_dev = variance ** 0.5

        if std_dev > 0:
            skew = sum(w * ((p - mean_price) / std_dev) ** 3 for p, w in zip(prices, volume_weights))
            kurtosis = sum(w * ((p - mean_price) / std_dev) ** 4 for p, w in zip(prices, volume_weights)) - 3
        else:
            skew = 0
            kurtosis = 0

        # Volume concentration (Herfindahl index)
        concentration = sum(w ** 2 for w in volume_weights)

        # Volume dispersion (price range weighted by volume)
        if len(prices) > 1:
            price_range = max(prices) - min(prices)
            dispersion = price_range * (1 - concentration)
        else:
            dispersion = 0

        return {
            'dominant_level': dominant_level,
            'max_ratio': max_imbalance_ratio,
            'total_volume': total_volume,
            'vwap': vwap,
            'skew': skew,
            'kurtosis': kurtosis,
            'level_count': len(prices),
            'concentration': concentration,
            'dispersion': dispersion
        }

    def _detect_footprint_patterns(self, dataframe: DataFrame) -> DataFrame:
        """Detect specific footprint patterns"""

        # Volume climax patterns
        dataframe['volume_climax_bull'] = (
            (dataframe['total_footprint_volume'] > dataframe['total_footprint_volume'].rolling(20).quantile(0.9)) &
            (dataframe['footprint_skew'] > 0) &
            (dataframe['close'] > dataframe['open'])
        )

        dataframe['volume_climax_bear'] = (
            (dataframe['total_footprint_volume'] > dataframe['total_footprint_volume'].rolling(20).quantile(0.9)) &
            (dataframe['footprint_skew'] < 0) &
            (dataframe['close'] < dataframe['open'])
        )

        # Volume vacuum patterns (low volume areas)
        dataframe['volume_vacuum'] = (
            (dataframe['total_footprint_volume'] < dataframe['total_footprint_volume'].rolling(20).quantile(0.2)) &
            (dataframe['volume_concentration'] > 0.8)
        )

        # Absorption patterns (high volume with small price movement)
        price_movement = abs(dataframe['close'] - dataframe['open'])
        avg_price_movement = price_movement.rolling(20).mean()

        dataframe['absorption_pattern'] = (
            (dataframe['total_footprint_volume'] > dataframe['total_footprint_volume'].rolling(20).mean() * 1.5) &
            (price_movement < avg_price_movement * 0.5) &
            (dataframe['max_imbalance_ratio'] > 5)
        )

        # Distribution patterns (volume spread across many levels)
        dataframe['distribution_pattern'] = (
            (dataframe['price_level_count'] > dataframe['price_level_count'].rolling(20).quantile(0.8)) &
            (dataframe['volume_concentration'] < 0.3) &
            (dataframe['volume_dispersion'] > dataframe['volume_dispersion'].rolling(20).mean())
        )

        # Accumulation patterns (volume concentrated at few levels)
        dataframe['accumulation_pattern'] = (
            (dataframe['volume_concentration'] > 0.7) &
            (dataframe['total_footprint_volume'] > dataframe['total_footprint_volume'].rolling(20).mean()) &
            (dataframe['price_level_count'] < dataframe['price_level_count'].rolling(20).quantile(0.3))
        )

        return dataframe

    def _analyze_volume_profile(self, dataframe: DataFrame) -> DataFrame:
        """Analyze volume profile characteristics"""

        # Volume profile shape analysis
        dataframe['vp_shape'] = dataframe.apply(self._classify_volume_profile_shape, axis=1)

        # Value area analysis (area containing 70% of volume)
        dataframe['value_area_high'] = dataframe['orderflow'].apply(self._calculate_value_area_high)
        dataframe['value_area_low'] = dataframe['orderflow'].apply(self._calculate_value_area_low)
        dataframe['value_area_width'] = dataframe['value_area_high'] - dataframe['value_area_low']

        # Price acceptance analysis
        dataframe['price_acceptance'] = dataframe.apply(self._analyze_price_acceptance, axis=1)

        return dataframe

    def _classify_volume_profile_shape(self, row):
        """Classify the shape of the volume profile"""
        concentration = row['volume_concentration']
        skew = row['footprint_skew']
        kurtosis = row['footprint_kurtosis']

        if concentration > 0.7:
            return 'single_print'  # Most volume at one level
        elif concentration < 0.3 and abs(skew) < 0.5:
            return 'balanced'  # Volume evenly distributed
        elif skew > 1:
            return 'bottom_heavy'  # More volume at lower prices
        elif skew < -1:
            return 'top_heavy'  # More volume at higher prices
        elif kurtosis > 2:
            return 'peaked'  # Sharp peak in volume
        elif kurtosis < -1:
            return 'flat'  # Flat volume distribution
        else:
            return 'normal'

    def _calculate_value_area_high(self, orderflow_dict):
        """Calculate the high of the value area (70% volume)"""
        if not orderflow_dict or not isinstance(orderflow_dict, dict):
            return None

        # Sort by volume descending
        sorted_levels = sorted(orderflow_dict.items(),
                             key=lambda x: x[1].get('total_volume', 0) if isinstance(x[1], dict) else 0,
                             reverse=True)

        total_volume = sum(data.get('total_volume', 0) for _, data in sorted_levels if isinstance(data, dict))
        target_volume = total_volume * 0.7

        cumulative_volume = 0
        value_area_prices = []

        for price_str, data in sorted_levels:
            if isinstance(data, dict):
                cumulative_volume += data.get('total_volume', 0)
                value_area_prices.append(float(price_str))

                if cumulative_volume >= target_volume:
                    break

        return max(value_area_prices) if value_area_prices else None

    def _calculate_value_area_low(self, orderflow_dict):
        """Calculate the low of the value area (70% volume)"""
        if not orderflow_dict or not isinstance(orderflow_dict, dict):
            return None

        # Sort by volume descending
        sorted_levels = sorted(orderflow_dict.items(),
                             key=lambda x: x[1].get('total_volume', 0) if isinstance(x[1], dict) else 0,
                             reverse=True)

        total_volume = sum(data.get('total_volume', 0) for _, data in sorted_levels if isinstance(data, dict))
        target_volume = total_volume * 0.7

        cumulative_volume = 0
        value_area_prices = []

        for price_str, data in sorted_levels:
            if isinstance(data, dict):
                cumulative_volume += data.get('total_volume', 0)
                value_area_prices.append(float(price_str))

                if cumulative_volume >= target_volume:
                    break

        return min(value_area_prices) if value_area_prices else None

    def _analyze_price_acceptance(self, row):
        """Analyze price acceptance based on volume profile"""
        close_price = row['close']
        value_area_high = row['value_area_high']
        value_area_low = row['value_area_low']

        if value_area_high is None or value_area_low is None:
            return 'unknown'

        if value_area_low <= close_price <= value_area_high:
            return 'accepted'  # Price closed within value area
        elif close_price > value_area_high:
            return 'above_value'  # Price closed above value area
        else:
            return 'below_value'  # Price closed below value area

    def _analyze_point_of_control(self, dataframe: DataFrame) -> DataFrame:
        """Analyze Point of Control (POC) - price level with highest volume"""

        # POC is the dominant price level
        dataframe['poc'] = dataframe['dominant_price_level']

        # POC relative to current price
        dataframe['poc_distance'] = (dataframe['close'] - dataframe['poc']) / dataframe['close']
        dataframe['poc_above'] = dataframe['poc'] > dataframe['close']
        dataframe['poc_below'] = dataframe['poc'] < dataframe['close']

        # POC strength (volume at POC relative to total volume)
        dataframe['poc_strength'] = dataframe['orderflow'].apply(self._calculate_poc_strength)

        # POC migration (how POC moves over time)
        dataframe['poc_migration'] = dataframe['poc'] - dataframe['poc'].shift(1)
        dataframe['poc_migration_rate'] = dataframe['poc_migration'].rolling(5).mean()

        # POC support/resistance
        dataframe['poc_support'] = (dataframe['poc_below'] == True) & (dataframe['poc_strength'] > 0.3)
        dataframe['poc_resistance'] = (dataframe['poc_above'] == True) & (dataframe['poc_strength'] > 0.3)

        return dataframe

    def _calculate_poc_strength(self, orderflow_dict):
        """Calculate the strength of the Point of Control"""
        if not orderflow_dict or not isinstance(orderflow_dict, dict):
            return 0

        volumes = [data.get('total_volume', 0) for data in orderflow_dict.values() if isinstance(data, dict)]

        if not volumes:
            return 0

        max_volume = max(volumes)
        total_volume = sum(volumes)

        return max_volume / total_volume if total_volume > 0 else 0

    def _calculate_confluence_score(self, dataframe: DataFrame) -> DataFrame:
        """Calculate comprehensive multi-indicator confluence score"""

        # Initialize component scores
        dataframe['delta_score'] = 0
        dataframe['cvd_score'] = 0
        dataframe['imbalance_score'] = 0
        dataframe['volume_score'] = 0
        dataframe['footprint_score'] = 0
        dataframe['divergence_score'] = 0

        # Calculate individual component scores
        dataframe = self._calculate_delta_score(dataframe)
        dataframe = self._calculate_cvd_score(dataframe)
        dataframe = self._calculate_imbalance_score(dataframe)
        dataframe = self._calculate_volume_score(dataframe)
        dataframe = self._calculate_footprint_score(dataframe)
        dataframe = self._calculate_divergence_score(dataframe)

        # Weighted composite score
        weights = {
            'delta': 0.25,
            'cvd': 0.25,
            'imbalance': 0.20,
            'volume': 0.15,
            'footprint': 0.10,
            'divergence': 0.05
        }

        dataframe['orderflow_score'] = (
            dataframe['delta_score'] * weights['delta'] +
            dataframe['cvd_score'] * weights['cvd'] +
            dataframe['imbalance_score'] * weights['imbalance'] +
            dataframe['volume_score'] * weights['volume'] +
            dataframe['footprint_score'] * weights['footprint'] +
            dataframe['divergence_score'] * weights['divergence']
        )

        # Adaptive scoring based on market regime
        dataframe = self._apply_regime_adjustments(dataframe)

        # Signal strength classification
        dataframe['signal_strength'] = self._classify_signal_strength(dataframe)

        # Confidence scoring
        dataframe['signal_confidence'] = self._calculate_signal_confidence(dataframe)

        return dataframe

    def _calculate_delta_score(self, dataframe: DataFrame) -> DataFrame:
        """Calculate delta component score (-100 to +100)"""
        score = pd.Series(0, index=dataframe.index)

        # Basic delta direction (40 points)
        score += (dataframe['delta'] > 0).astype(int) * 40
        score -= (dataframe['delta'] < 0).astype(int) * 40

        # Delta strength (30 points)
        score += dataframe['delta_strong'].astype(int) * 30 * np.sign(dataframe['delta'])

        # Delta momentum (20 points)
        score += (dataframe['delta_momentum'] > 0).astype(int) * 20
        score -= (dataframe['delta_momentum'] < 0).astype(int) * 20

        # Delta trend (10 points)
        score += dataframe['delta_trend_up'].astype(int) * 10
        score -= dataframe['delta_trend_down'].astype(int) * 10

        # Delta extremes (bonus/penalty)
        score += dataframe['delta_extreme_positive'].astype(int) * 15
        score -= dataframe['delta_extreme_negative'].astype(int) * 15

        # Delta exhaustion (penalty)
        score -= dataframe['delta_exhaustion_bull'].astype(int) * 25
        score += dataframe['delta_exhaustion_bear'].astype(int) * 25

        dataframe['delta_score'] = np.clip(score, -100, 100)
        return dataframe

    def _calculate_cvd_score(self, dataframe: DataFrame) -> DataFrame:
        """Calculate CVD component score (-100 to +100)"""
        score = pd.Series(0, index=dataframe.index)

        # CVD trend (40 points)
        score += dataframe['cvd_trend_up'].astype(int) * 40
        score -= dataframe['cvd_trend_down'].astype(int) * 40

        # CVD slope (30 points)
        normalized_slope = np.tanh(dataframe['cvd_slope'] / dataframe['cvd_slope'].std()) * 30
        score += normalized_slope

        # CVD momentum (20 points)
        score += (dataframe['cvd_momentum'] > 0).astype(int) * 20
        score -= (dataframe['cvd_momentum'] < 0).astype(int) * 20

        # CVD regime (10 points)
        score += (dataframe['cvd_regime'] == 'strong_accumulation').astype(int) * 10
        score -= (dataframe['cvd_regime'] == 'strong_distribution').astype(int) * 10
        score += (dataframe['cvd_regime'] == 'accumulation').astype(int) * 5
        score -= (dataframe['cvd_regime'] == 'distribution').astype(int) * 5

        # CVD breakouts (bonus)
        score += dataframe['cvd_breakout_bull'].astype(int) * 20
        score -= dataframe['cvd_breakout_bear'].astype(int) * 20

        # CVD exhaustion (penalty)
        score -= dataframe['cvd_exhaustion_bull'].astype(int) * 25
        score += dataframe['cvd_exhaustion_bear'].astype(int) * 25

        dataframe['cvd_score'] = np.clip(score, -100, 100)
        return dataframe

    def _calculate_imbalance_score(self, dataframe: DataFrame) -> DataFrame:
        """Calculate imbalance component score (-100 to +100)"""
        score = pd.Series(0, index=dataframe.index)

        # Stacked imbalances (50 points)
        score += dataframe['strong_bid_stack'].astype(int) * 50
        score -= dataframe['strong_ask_stack'].astype(int) * 50

        # Imbalance strength (30 points)
        score += dataframe['bid_imbalance_strength'] * 30
        score -= dataframe['ask_imbalance_strength'] * 30

        # Imbalance momentum (20 points)
        score += dataframe['imbalance_momentum_bull'].astype(int) * 20
        score -= dataframe['imbalance_momentum_bear'].astype(int) * 20

        # Imbalance signals (bonus)
        score += dataframe['imbalance_bullish_signal'].astype(int) * 25
        score -= dataframe['imbalance_bearish_signal'].astype(int) * 25

        # Imbalance exhaustion (penalty)
        score -= dataframe['imbalance_exhaustion_bull'].astype(int) * 30
        score += dataframe['imbalance_exhaustion_bear'].astype(int) * 30

        dataframe['imbalance_score'] = np.clip(score, -100, 100)
        return dataframe

    def _calculate_volume_score(self, dataframe: DataFrame) -> DataFrame:
        """Calculate volume component score (-100 to +100)"""
        score = pd.Series(0, index=dataframe.index)

        # Volume surge (40 points)
        volume_ratio = dataframe['volume'] / dataframe['volume_sma']
        volume_surge = np.clip((volume_ratio - 1) * 40, 0, 40)
        score += volume_surge

        # Volume trend (30 points)
        volume_trend = (dataframe['volume_sma'] > dataframe['volume_sma'].shift(5)).astype(int) * 30
        score += volume_trend

        # Volume patterns (30 points)
        score += dataframe['volume_climax_bull'].astype(int) * 30
        score -= dataframe['volume_climax_bear'].astype(int) * 30
        score -= dataframe['volume_vacuum'].astype(int) * 20  # Penalty for low volume

        dataframe['volume_score'] = np.clip(score, -100, 100)
        return dataframe

    def _calculate_footprint_score(self, dataframe: DataFrame) -> DataFrame:
        """Calculate footprint component score (-100 to +100)"""
        score = pd.Series(0, index=dataframe.index)

        # Volume profile patterns (40 points)
        score += dataframe['accumulation_pattern'].astype(int) * 40
        score -= dataframe['distribution_pattern'].astype(int) * 40

        # Absorption patterns (30 points)
        score += dataframe['absorption_pattern'].astype(int) * 30

        # POC analysis (20 points)
        score += dataframe['poc_support'].astype(int) * 20
        score -= dataframe['poc_resistance'].astype(int) * 20

        # Volume concentration (10 points)
        concentration_score = (dataframe['volume_concentration'] - 0.5) * 20
        score += concentration_score

        dataframe['footprint_score'] = np.clip(score, -100, 100)
        return dataframe

    def _calculate_divergence_score(self, dataframe: DataFrame) -> DataFrame:
        """Calculate divergence component score (-100 to +100)"""
        score = pd.Series(0, index=dataframe.index)

        # Regular divergences (60 points)
        score -= dataframe['bearish_divergence'].astype(int) * 60
        score += dataframe['bullish_divergence'].astype(int) * 60

        # Hidden divergences (40 points)
        score -= dataframe['hidden_bearish_divergence'].astype(int) * 40
        score += dataframe['hidden_bullish_divergence'].astype(int) * 40

        # Divergence strength (bonus)
        score -= dataframe['bearish_divergence_strength'] * 10
        score += dataframe['bullish_divergence_strength'] * 10

        dataframe['divergence_score'] = np.clip(score, -100, 100)
        return dataframe

    def _apply_regime_adjustments(self, dataframe: DataFrame) -> DataFrame:
        """Apply regime-based adjustments to the confluence score"""

        # Adjust scores based on delta regime
        trending_mask = dataframe['delta_regime'] == 'trending'
        volatile_mask = dataframe['delta_regime'] == 'volatile'

        # In trending markets, give more weight to momentum indicators
        dataframe.loc[trending_mask, 'orderflow_score'] *= 1.1

        # In volatile markets, reduce confidence
        dataframe.loc[volatile_mask, 'orderflow_score'] *= 0.9

        # Adjust based on CVD regime
        strong_accumulation_mask = dataframe['cvd_regime'] == 'strong_accumulation'
        strong_distribution_mask = dataframe['cvd_regime'] == 'strong_distribution'

        # Boost bullish signals during strong accumulation
        dataframe.loc[strong_accumulation_mask & (dataframe['orderflow_score'] > 0), 'orderflow_score'] *= 1.2

        # Boost bearish signals during strong distribution
        dataframe.loc[strong_distribution_mask & (dataframe['orderflow_score'] < 0), 'orderflow_score'] *= 1.2

        return dataframe

    def _classify_signal_strength(self, dataframe: DataFrame) -> DataFrame:
        """Classify signal strength based on confluence score"""
        strength = pd.Series('weak', index=dataframe.index)

        abs_score = abs(dataframe['orderflow_score'])

        strength.loc[abs_score >= 60, 'signal_strength'] = 'medium'
        strength.loc[abs_score >= self.strong_signal_threshold.value, 'signal_strength'] = 'strong'
        strength.loc[abs_score >= 95, 'signal_strength'] = 'extreme'

        return strength

    def _calculate_signal_confidence(self, dataframe: DataFrame) -> DataFrame:
        """Calculate confidence level for signals (0-1)"""

        # Base confidence from score magnitude
        base_confidence = abs(dataframe['orderflow_score']) / 100

        # Adjust based on component agreement
        component_scores = [
            dataframe['delta_score'],
            dataframe['cvd_score'],
            dataframe['imbalance_score'],
            dataframe['volume_score'],
            dataframe['footprint_score']
        ]

        # Count how many components agree on direction
        positive_components = sum((score > 20).astype(int) for score in component_scores)
        negative_components = sum((score < -20).astype(int) for score in component_scores)

        agreement_ratio = np.maximum(positive_components, negative_components) / len(component_scores)

        # Final confidence combines magnitude and agreement
        confidence = base_confidence * 0.7 + agreement_ratio * 0.3

        return np.clip(confidence, 0, 1)

    def _add_technical_indicators(self, dataframe: DataFrame) -> DataFrame:
        """Add traditional technical indicators for confirmation"""
        # RSI
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        
        # Moving averages
        dataframe['ema_20'] = ta.EMA(dataframe, timeperiod=20)
        dataframe['ema_50'] = ta.EMA(dataframe, timeperiod=50)
        
        # Bollinger Bands
        bollinger = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)
        dataframe['bb_lower'] = bollinger['lowerband']
        dataframe['bb_middle'] = bollinger['middleband']
        dataframe['bb_upper'] = bollinger['upperband']
        
        # Volume indicators
        dataframe['volume_sma'] = ta.SMA(dataframe['volume'], timeperiod=20)
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the entry signal for the given dataframe
        :param dataframe: DataFrame
        :param metadata: Additional information, like the currently traded pair
        :return: DataFrame with entry columns populated
        """

        # Long entry conditions
        dataframe.loc[
            (
                # Strong orderflow confluence
                (dataframe['orderflow_score'] >= self.min_confluence_score.value) &

                # Delta confirmation
                (dataframe['delta'] > 0) &
                (dataframe['delta_strong'] == True) &
                (dataframe['delta_trend_up'] == True) &

                # CVD confirmation
                (dataframe['cvd_trend_up'] == True) &
                (dataframe['cvd_slope'] > 0) &

                # Volume confirmation
                (dataframe['volume'] > dataframe['volume_sma']) &

                # Technical confirmation
                (dataframe['close'] > dataframe['ema_20']) &
                (dataframe['rsi'] > 30) & (dataframe['rsi'] < 70) &

                # No recent bearish divergence
                (dataframe['bearish_divergence'] == False)
            ),
            ['enter_long', 'enter_tag']
        ] = (1, 'orderflow_long')

        # Strong signal long entries (higher confluence)
        dataframe.loc[
            (
                # Very strong orderflow confluence
                (dataframe['orderflow_score'] >= self.strong_signal_threshold.value) &
                (dataframe['signal_strength'] == 'strong') &

                # Strong delta and CVD alignment
                (dataframe['delta'] > 0) &
                (dataframe['delta_momentum'] > 0) &
                (dataframe['cvd_trend_up'] == True) &

                # Strong imbalance support
                (dataframe['strong_bid_stack'] == True) &
                (dataframe['max_imbalance_ratio'] > 5) &

                # Technical alignment
                (dataframe['close'] > dataframe['ema_20']) &
                (dataframe['close'] > dataframe['ema_50']) &

                # Volume surge
                (dataframe['volume'] > dataframe['volume_sma'] * 1.5)
            ),
            ['enter_long', 'enter_tag']
        ] = (1, 'strong_orderflow_long')

        # Short entry conditions
        dataframe.loc[
            (
                # Strong bearish orderflow confluence
                (dataframe['orderflow_score'] <= -self.min_confluence_score.value) &

                # Delta confirmation
                (dataframe['delta'] < 0) &
                (dataframe['delta_strong'] == True) &
                (dataframe['delta_trend_down'] == True) &

                # CVD confirmation
                (dataframe['cvd_trend_down'] == True) &
                (dataframe['cvd_slope'] < 0) &

                # Volume confirmation
                (dataframe['volume'] > dataframe['volume_sma']) &

                # Technical confirmation
                (dataframe['close'] < dataframe['ema_20']) &
                (dataframe['rsi'] > 30) & (dataframe['rsi'] < 70) &

                # No recent bullish divergence
                (dataframe['bullish_divergence'] == False)
            ),
            ['enter_short', 'enter_tag']
        ] = (1, 'orderflow_short')

        # Strong signal short entries
        dataframe.loc[
            (
                # Very strong bearish orderflow confluence
                (dataframe['orderflow_score'] <= -self.strong_signal_threshold.value) &
                (dataframe['signal_strength'] == 'strong') &

                # Strong delta and CVD alignment
                (dataframe['delta'] < 0) &
                (dataframe['delta_momentum'] < 0) &
                (dataframe['cvd_trend_down'] == True) &

                # Strong imbalance resistance
                (dataframe['strong_ask_stack'] == True) &
                (dataframe['max_imbalance_ratio'] > 5) &

                # Technical alignment
                (dataframe['close'] < dataframe['ema_20']) &
                (dataframe['close'] < dataframe['ema_50']) &

                # Volume surge
                (dataframe['volume'] > dataframe['volume_sma'] * 1.5)
            ),
            ['enter_short', 'enter_tag']
        ] = (1, 'strong_orderflow_short')

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the exit signal for the given dataframe
        :param dataframe: DataFrame
        :param metadata: Additional information, like the currently traded pair
        :return: DataFrame with exit columns populated
        """

        # Long exit conditions
        dataframe.loc[
            (
                # Orderflow reversal
                (dataframe['orderflow_score'] < -30) |

                # Delta reversal
                (dataframe['delta'] < 0) & (dataframe['delta_momentum'] < 0) |

                # CVD divergence
                (dataframe['bearish_divergence'] == True) |

                # Strong ask imbalances
                (dataframe['strong_ask_stack'] == True) & (dataframe['max_imbalance_ratio'] > 7) |

                # Technical exit
                (dataframe['close'] < dataframe['ema_20']) & (dataframe['rsi'] > 75)
            ),
            ['exit_long', 'exit_tag']
        ] = (1, 'orderflow_exit_long')

        # Short exit conditions
        dataframe.loc[
            (
                # Orderflow reversal
                (dataframe['orderflow_score'] > 30) |

                # Delta reversal
                (dataframe['delta'] > 0) & (dataframe['delta_momentum'] > 0) |

                # CVD divergence
                (dataframe['bullish_divergence'] == True) |

                # Strong bid imbalances
                (dataframe['strong_bid_stack'] == True) & (dataframe['max_imbalance_ratio'] > 7) |

                # Technical exit
                (dataframe['close'] > dataframe['ema_20']) & (dataframe['rsi'] < 25)
            ),
            ['exit_short', 'exit_tag']
        ] = (1, 'orderflow_exit_short')

        return dataframe
