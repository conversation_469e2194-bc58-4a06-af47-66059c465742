---
title: "freqUI - Freqtrade"
meta:
  description: "Freqtrade is a free and open source crypto trading bot written in Python, designed to support all major exchanges and be controlled via Telegram or builtin Web UI"
---

[Edit this page](https://github.com/freqtrade/freqtrade/edit/develop/docs/freq-ui.md)

# FreqUI [¶](https://www.freqtrade.io/#frequi "Permanent link")

Freqtrade provides a builtin webserver, which can serve [FreqUI](https://github.com/freqtrade/frequi), the freqtrade frontend.

By default, the UI is automatically installed as part of the installation (script, docker). freqUI can also be manually installed by using the `freqtrade install-ui` command. This same command can also be used to update freqUI to new new releases.

Once the bot is started in trade / dry-run mode (with `freqtrade trade`) - the UI will be available under the configured API port (by default `http://127.0.0.1:8080`).

<details>

<summary>Looking to contribute to freqUI?</summary>



Developers should not use this method, but instead clone the corresponding use the method described in the [freqUI repository](https://github.com/freqtrade/frequi) to get the source-code of freqUI. A working installation of node will be required to build the frontend.

</details>

freqUI is not required to run freqtrade

freqUI is an optional component of freqtrade, and is not required to run the bot. It is a frontend that can be used to monitor the bot and to interact with it - but freqtrade itself will work perfectly fine without it.

## Configuration [¶](https://www.freqtrade.io/#configuration "Permanent link")

FreqUI does not have it's own configuration file - but assumes a working setup for the [rest-api](https://www.freqtrade.io/../rest-api/) is available. Please refer to the corresponding documentation page to get setup with freqUI

## UI [¶](https://www.freqtrade.io/#ui "Permanent link")

FreqUI is a modern, responsive web application that can be used to monitor and interact with your bot.

FreqUI provides a light, as well as a dark theme. Themes can be easily switched via a prominent button at the top of the page. The theme of the screenshots on this page will adapt to the selected documentation Theme, so to see the dark (or light) version, please switch the theme of the Documentation.

### Login [¶](https://www.freqtrade.io/#login "Permanent link")

The below screenshot shows the login screen of freqUI.

![FreqUI - login](https://www.freqtrade.io/../assets/frequi-login-CORS.png#only-dark)![FreqUI - login](https://www.freqtrade.io/../assets/frequi-login-CORS-light.png#only-light)

CORS

The Cors error shown in this screenshot is due to the fact that the UI is running on a different port than the API, and [CORS](https://www.freqtrade.io/#cors) has not been setup correctly yet.

### Trade view [¶](https://www.freqtrade.io/#trade-view "Permanent link")

The trade view allows you to visualize the trades that the bot is making and to interact with the bot. On this page, you can also interact with the bot by starting and stopping it and - if configured - force trade entries and exits.

![FreqUI - trade view](https://www.freqtrade.io/../assets/freqUI-trade-pane-dark.png#only-dark)![FreqUI - trade view](https://www.freqtrade.io/../assets/freqUI-trade-pane-light.png#only-light)

### Plot Configurator [¶](https://www.freqtrade.io/#plot-configurator "Permanent link")

FreqUI Plots can be configured either via a `plot_config` configuration object in the strategy (which can be loaded via "from strategy" button) or via the UI. Multiple plot configurations can be created and switched at will - allowing for flexible, different views into your charts.

The plot configuration can be accessed via the "Plot Configurator" (Cog icon) button in the top right corner of the trade view.

![FreqUI - plot configuration](https://www.freqtrade.io/../assets/freqUI-plot-configurator-dark.png#only-dark)![FreqUI - plot configuration](https://www.freqtrade.io/../assets/freqUI-plot-configurator-light.png#only-light)

### Settings [¶](https://www.freqtrade.io/#settings "Permanent link")

Several UI related settings can be changed by accessing the settings page.

Things you can change (among others):

- Timezone of the UI
- Visualization of open trades as part of the favicon (browser tab)
- Candle colors (up/down -> red/green)
- Enable / disable in-app notification types

![FreqUI - Settings view](https://www.freqtrade.io/../assets/frequi-settings-dark.png#only-dark)![FreqUI - Settings view](https://www.freqtrade.io/../assets/frequi-settings-light.png#only-light)

## Backtesting [¶](https://www.freqtrade.io/#backtesting "Permanent link")

When freqtrade is started in [webserver mode](https://www.freqtrade.io/../utils/#webserver-mode) (freqtrade started with `freqtrade webserver`), the backtesting view becomes available. This view allows you to backtest strategies and visualize the results.

You can also load and visualize previous backtest results, as well as compare the results with each other.

![FreqUI - Backtesting](https://www.freqtrade.io/../assets/freqUI-backtesting-dark.png#only-dark)![FreqUI - Backtesting](https://www.freqtrade.io/../assets/freqUI-backtesting-light.png#only-light)

## CORS [¶](https://www.freqtrade.io/#cors "Permanent link")

This whole section is only necessary in cross-origin cases (where you multiple bot API's running on `localhost:8081`, `localhost:8082`, ...), and want to combine them into one FreqUI instance.

<details>

<summary>Technical explanation</summary>



All web-based front-ends are subject to [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) - Cross-Origin Resource Sharing. Since most of the requests to the Freqtrade API must be authenticated, a proper CORS policy is key to avoid security problems. Also, the standard disallows `*` CORS policies for requests with credentials, so this setting must be set appropriately.

</details>

Users can allow access from different origin URL's to the bot API via the `CORS_origins` configuration setting. It consists of a list of allowed URL's that are allowed to consume resources from the bot's API.

Assuming your application is deployed as `https://frequi.freqtrade.io/home/<USER>

```
{
    //...
    "jwt_secret_key": "somethingrandom",
    "CORS_origins": ["https://frequi.freqtrade.io"],
    //...
}
```

In the following (pretty common) case, FreqUI is accessible on `http://localhost:8080/trade` (this is what you see in your navbar when navigating to freqUI). ![freqUI url](https://www.freqtrade.io/../assets/frequi_url.png)

The correct configuration for this case is `http://localhost:8080` - the main part of the URL including the port.

```
{
    //...
    "jwt_secret_key": "somethingrandom",
    "CORS_origins": ["http://localhost:8080"],
    //...
}
```

trailing Slash

The trailing slash is not allowed in the `CORS_origins` configuration (e.g. `"http://localhots:8080/"`). Such a configuration will not take effect, and the cors errors will remain.

Note

We strongly recommend to also set `jwt_secret_key` to something random and known only to yourself to avoid unauthorized access to your bot.