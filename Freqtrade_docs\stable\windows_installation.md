---
title: "Windows - Freqtrade"
meta:
  description: "Freqtrade is a free and open source crypto trading bot written in Python, designed to support all major exchanges and be controlled via Telegram or builtin Web UI"
---

[Edit this page](https://github.com/freqtrade/freqtrade/edit/develop/docs/windows_installation.md)

# Windows installation [¶](https://www.freqtrade.io/#windows-installation "Permanent link")

We **strongly** recommend that Windows users use [Docker](https://www.freqtrade.io/../docker_quickstart/) as this will work much easier and smoother (also more secure).

If that is not possible, try using the Windows Linux subsystem (WSL) - for which the Ubuntu instructions should work. Otherwise, please follow the instructions below.

All instructions assume that python 3.10+ is installed and available.

## Clone the git repository [¶](https://www.freqtrade.io/#clone-the-git-repository "Permanent link")

First of all clone the repository by running:

```
git clone https://github.com/freqtrade/freqtrade.git
```

Now, choose your installation method, either automatically via script (recommended) or manually following the corresponding instructions.

## Install freqtrade automatically [¶](https://www.freqtrade.io/#install-freqtrade-automatically "Permanent link")

### Run the installation script [¶](https://www.freqtrade.io/#run-the-installation-script "Permanent link")

The script will ask you a few questions to determine which parts should be installed.

```
Set-ExecutionPolicy -ExecutionPolicy Bypass
cd freqtrade
. .\setup.ps1
```

## Install freqtrade manually [¶](https://www.freqtrade.io/#install-freqtrade-manually "Permanent link")

64bit Python version

Please make sure to use 64bit Windows and 64bit Python to avoid problems with backtesting or hyperopt due to the memory constraints 32bit applications have under Windows. 32bit python versions are no longer supported under Windows.

Hint

Using the [Anaconda Distribution](https://www.anaconda.com/distribution/) under Windows can greatly help with installation problems. Check out the [Anaconda installation section](https://www.freqtrade.io/../installation/#installation-with-conda) in the documentation for more information.

### Install ta-lib [¶](https://www.freqtrade.io/#install-ta-lib "Permanent link")

Install ta-lib according to the [ta-lib documentation](https://github.com/TA-Lib/ta-lib-python#windows).

As compiling from source on windows has heavy dependencies (requires a partial visual studio installation), Freqtrade provides these dependencies (in the binary wheel format) for the latest 3 Python versions (3.10, 3.11, 3.12 and 3.13) and for 64bit Windows. These Wheels are also used by CI running on windows, and are therefore tested together with freqtrade.

Other versions must be downloaded from the above link.

```
cd \path\freqtrade
python -m venv .venv
.venv\Scripts\activate.ps1
# optionally install ta-lib from wheel
# Eventually adjust the below filename to match the downloaded wheel
pip install --find-links build_helpers\ TA-Lib -U
pip install -r requirements.txt
pip install -e .
freqtrade
```

Use Powershell

The above installation script assumes you're using powershell on a 64bit windows. Commands for the legacy CMD windows console may differ.

### Error during installation on Windows [¶](https://www.freqtrade.io/#error-during-installation-on-windows "Permanent link")

```
error: Microsoft Visual C++ 14.0 is required. Get it with "Microsoft Visual C++ Build Tools": http://landinghub.visualstudio.com/visual-cpp-build-tools
```

Unfortunately, many packages requiring compilation don't provide a pre-built wheel. It is therefore mandatory to have a C/C++ compiler installed and available for your python environment to use.

You can download the Visual C++ build tools from [here](https://visualstudio.microsoft.com/visual-cpp-build-tools/) and install "Desktop development with C++" in it's default configuration. Unfortunately, this is a heavy download / dependency so you might want to consider WSL2 or [docker compose](https://www.freqtrade.io/../docker_quickstart/) first.

![Windows installation](https://www.freqtrade.io/../assets/windows_install.png)

---