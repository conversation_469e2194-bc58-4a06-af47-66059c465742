---
title: "Deprecated Features - Freqtrade"
meta:
  description: "Freqtrade is a free and open source crypto trading bot written in Python, designed to support all major exchanges and be controlled via Telegram or builtin Web UI"
---

[Edit this page](https://github.com/freqtrade/freqtrade/edit/develop/docs/deprecated.md)

# Deprecated features [¶](https://www.freqtrade.io/#deprecated-features "Permanent link")

This page contains description of the command line arguments, configuration parameters and the bot features that were declared as DEPRECATED by the bot development team and are no longer supported. Please avoid their usage in your configuration.

## Removed features [¶](https://www.freqtrade.io/#removed-features "Permanent link")

### the `--refresh-pairs-cached` command line option [¶](https://www.freqtrade.io/#the-refresh-pairs-cached-command-line-option "Permanent link")

`--refresh-pairs-cached` in the context of backtesting, hyperopt and edge allows to refresh candle data for backtesting. Since this leads to much confusion, and slows down backtesting (while not being part of backtesting) this has been singled out as a separate freqtrade sub-command `freqtrade download-data`.

This command line option was deprecated in 2019.7-dev (develop branch) and removed in 2019.9.

### The **--dynamic-whitelist** command line option [¶](https://www.freqtrade.io/#the-dynamic-whitelist-command-line-option "Permanent link")

This command line option was deprecated in 2018 and removed freqtrade 2019.6-dev (develop branch) and in freqtrade 2019.7. Please refer to [pairlists](https://www.freqtrade.io/../plugins/#pairlists-and-pairlist-handlers) instead.

### the `--live` command line option [¶](https://www.freqtrade.io/#the-live-command-line-option "Permanent link")

`--live` in the context of backtesting allowed to download the latest tick data for backtesting. Did only download the latest 500 candles, so was ineffective in getting good backtest data. Removed in 2019-7-dev (develop branch) and in freqtrade 2019.8.

### `ticker_interval` (now `timeframe`) [¶](https://www.freqtrade.io/#ticker_interval-now-timeframe "Permanent link")

Support for `ticker_interval` terminology was deprecated in 2020.6 in favor of `timeframe` - and compatibility code was removed in 2022.3.

### Allow running multiple pairlists in sequence [¶](https://www.freqtrade.io/#allow-running-multiple-pairlists-in-sequence "Permanent link")

The former `"pairlist"` section in the configuration has been removed, and is replaced by `"pairlists"` - being a list to specify a sequence of pairlists.

The old section of configuration parameters (`"pairlist"`) has been deprecated in 2019.11 and has been removed in 2020.4.

### deprecation of bidVolume and askVolume from volume-pairlist [¶](https://www.freqtrade.io/#deprecation-of-bidvolume-and-askvolume-from-volume-pairlist "Permanent link")

Since only quoteVolume can be compared between assets, the other options (bidVolume, askVolume) have been deprecated in 2020.4, and have been removed in 2020.9.

### Using order book steps for exit price [¶](https://www.freqtrade.io/#using-order-book-steps-for-exit-price "Permanent link")

Using `order_book_min` and `order_book_max` used to allow stepping the orderbook and trying to find the next ROI slot - trying to place sell-orders early. As this does however increase risk and provides no benefit, it's been removed for maintainability purposes in 2021.7.

### Legacy Hyperopt mode [¶](https://www.freqtrade.io/#legacy-hyperopt-mode "Permanent link")

Using separate hyperopt files was deprecated in 2021.4 and was removed in 2021.9. Please switch to the new [Parametrized Strategies](https://www.freqtrade.io/../hyperopt/) to benefit from the new hyperopt interface.

## Strategy changes between V2 and V3 [¶](https://www.freqtrade.io/#strategy-changes-between-v2-and-v3 "Permanent link")

Isolated Futures / short trading was introduced in 2022.4. This required major changes to configuration settings, strategy interfaces, ...

We have put a great effort into keeping compatibility with existing strategies, so if you just want to continue using freqtrade in spot markets, there are no changes necessary. While we may drop support for the current interface sometime in the future, we will announce this separately and have an appropriate transition period.

Please follow the [Strategy migration](https://www.freqtrade.io/../strategy_migration/) guide to migrate your strategy to the new format to start using the new functionalities.

### webhooks - changes with 2022.4 [¶](https://www.freqtrade.io/#webhooks-changes-with-20224 "Permanent link")

#### `buy_tag` has been renamed to `enter_tag`[¶](https://www.freqtrade.io/#buy_tag-has-been-renamed-to-enter_tag "Permanent link")

This should apply only to your strategy and potentially to webhooks. We will keep a compatibility layer for 1-2 versions (so both `buy_tag` and `enter_tag` will still work), but support for this in webhooks will disappear after that.

#### Naming changes [¶](https://www.freqtrade.io/#naming-changes "Permanent link")

Webhook terminology changed from "sell" to "exit", and from "buy" to "entry", removing "webhook" in the process.

- `webhookbuy`, `webhookentry` -> `entry`
- `webhookbuyfill`, `webhookentryfill` -> `entry_fill`
- `webhookbuycancel`, `webhookentrycancel` -> `entry_cancel`
- `webhooksell`, `webhookexit` -> `exit`
- `webhooksellfill`, `webhookexitfill` -> `exit_fill`
- `webhooksellcancel`, `webhookexitcancel` -> `exit_cancel`

## Removal of `populate_any_indicators`[¶](https://www.freqtrade.io/#removal-of-populate_any_indicators "Permanent link")

version 2023.3 saw the removal of `populate_any_indicators` in favor of split methods for feature engineering and targets. Please read the [migration document](https://www.freqtrade.io/../strategy_migration/#freqai-strategy) for full details.

## Removal of `protections` from configuration [¶](https://www.freqtrade.io/#removal-of-protections-from-configuration "Permanent link")

Setting protections from the configuration via `"protections": [],` has been removed in 2024.10, after having raised deprecation warnings for over 3 years.

## hdf5 data storage [¶](https://www.freqtrade.io/#hdf5-data-storage "Permanent link")

Using hdf5 as data storage has been deprecated in 2024.12 and was removed in 2025.1. We recommend switching to the feather data format.

Please use the [`convert-data` subcommand](https://www.freqtrade.io/../data-download/#sub-command-convert-data) to convert your existing data to one of the supported formats before updating.

## Configuring advanced logging via config [¶](https://www.freqtrade.io/#configuring-advanced-logging-via-config "Permanent link")

Configuring syslog and journald via `--logfile systemd` and `--logfile journald` respectively has been deprecated in 2025.3. Please use configuration based [log setup](https://www.freqtrade.io/../advanced-setup/#advanced-logging) instead.

## Removal of the edge module [¶](https://www.freqtrade.io/#removal-of-the-edge-module "Permanent link")

The edge module has been deprecated in 2023.9 and removed in 2025.6. All functionalities of edge have been removed, and having edge configured will result in an error.