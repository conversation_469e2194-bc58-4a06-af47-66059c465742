---
title: "Updating Freqtrade - Freqtrade"
meta:
  description: "Freqtrade is a free and open source crypto trading bot written in Python, designed to support all major exchanges and be controlled via Telegram or builtin Web UI"
---

[Edit this page](https://github.com/freqtrade/freqtrade/edit/develop/docs/updating.md)

# How to update [¶](https://www.freqtrade.io/#how-to-update "Permanent link")

To update your freqtrade installation, please use one of the below methods, corresponding to your installation method.

Tracking changes

Breaking changes / changed behavior will be documented in the changelog that is posted alongside every release. For the develop branch, please follow PR's to avoid being surprised by changes.

## Docker [¶](https://www.freqtrade.io/#docker "Permanent link")

Legacy installations using the `master` image

We're switching from master to stable for the release Images - please adjust your docker-file and replace `freqtradeorg/freqtrade:master` with `freqtradeorg/freqtrade:stable`

```
docker compose pull
docker compose up -d
```

## Installation via setup script [¶](https://www.freqtrade.io/#installation-via-setup-script "Permanent link")

```
./setup.sh --update
```

Note

Make sure to run this command with your virtual environment disabled!

## Plain native installation [¶](https://www.freqtrade.io/#plain-native-installation "Permanent link")

Please ensure that you're also updating dependencies - otherwise things might break without you noticing.

```
git pull
pip install -U -r requirements.txt
pip install -e .

# Ensure freqUI is at the latest version
freqtrade install-ui 
```

### Problems updating [¶](https://www.freqtrade.io/#problems-updating "Permanent link")

Update-problems usually come missing dependencies (you didn't follow the above instructions) - or from updated dependencies, which fail to install (for example TA-lib). Please refer to the corresponding installation sections (common problems linked below)

Common problems and their solutions:

- [ta-lib update on windows](https://www.freqtrade.io/../windows_installation/#install-ta-lib)