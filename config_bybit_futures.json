{"max_open_trades": 5, "stake_currency": "USDT", "stake_amount": 500, "tradable_balance_ratio": 0.95, "fiat_display_currency": "USD", "timeframe": "5m", "dry_run": true, "cancel_open_orders_on_exit": true, "exchange": {"name": "bybit", "key": "your_bybit_api_key", "secret": "your_bybit_api_secret", "ccxt_config": {"enableRateLimit": true, "rateLimit": 120, "timeout": 30000, "options": {"defaultType": "linear"}}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "BNB/USDT:USDT", "ADA/USDT:USDT", "MATIC/USDT:USDT", "DOT/USDT:USDT", "AVAX/USDT:USDT"], "pair_blacklist": []}, "trading_mode": "futures", "margin_mode": "isolated", "orderflow": {"use_public_trades": true, "cache_size": 1500, "scale": 0.4}, "strategy": "OrderflowStrategy", "strategy_path": "strategies/", "stoploss": -0.04, "trailing_stop": true, "trailing_stop_positive": 0.008, "trailing_stop_positive_offset": 0.015, "trailing_only_offset_is_reached": true, "protections": [{"method": "<PERSON><PERSON><PERSON><PERSON>", "lookback_period_candles": 60, "trade_limit": 4, "stop_duration_candles": 60, "only_per_pair": false}, {"method": "MaxDrawdown", "lookback_period_candles": 200, "trade_limit": 20, "stop_duration_candles": 100, "max_allowed_drawdown": 0.2}, {"method": "LowProfitPairs", "lookbook_period_candles": 1440, "trade_limit": 20, "stop_duration_candles": 60, "required_profit": 0.015}], "orderflow_strategy_params": {"delta_period": 20, "cvd_period": 50, "min_confluence_score": 60, "strong_signal_threshold": 80, "imbalance_threshold": 3.0, "volume_threshold": 1.5, "divergence_lookback": 20, "exhaustion_threshold": 0.8}, "telegram": {"enabled": false, "token": "your_telegram_bot_token", "chat_id": "your_chat_id", "notification_settings": {"status": "on", "warning": "on", "startup": "on", "buy": "on", "sell": "on", "buy_cancel": "on", "sell_cancel": "on", "buy_fill": "on", "sell_fill": "on", "protection_trigger": "on", "protection_trigger_global": "on"}}, "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "info", "enable_openapi": true, "jwt_secret_key": "your_jwt_secret_key", "CORS_origins": ["http://localhost:3000"], "username": "freqtrade", "password": "your_password"}, "logging": {"verbosity": 2, "logfile": "logs/freqtrade_bybit.log"}, "internals": {"process_throttle_secs": 5, "heartbeat_interval": 60}}