#!/usr/bin/env python3
"""
Strategy validation script for OrderflowStrategy
Tests strategy code without requiring actual market data
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

# Add the strategies directory to the path
sys.path.append('strategies')

try:
    from OrderflowStrategy import OrderflowStrategy
    print("✅ Successfully imported OrderflowStrategy")
except ImportError as e:
    print(f"❌ Failed to import OrderflowStrategy: {e}")
    sys.exit(1)

def create_mock_dataframe(length=200):
    """Create mock OHLCV data for testing"""
    
    # Generate timestamps
    start_time = datetime.now() - timedelta(minutes=length * 5)
    timestamps = [start_time + timedelta(minutes=i * 5) for i in range(length)]
    
    # Generate realistic price data
    base_price = 50000  # Starting price
    prices = []
    current_price = base_price
    
    for i in range(length):
        # Add some random walk with trend
        change = np.random.normal(0, 0.002)  # 0.2% average change
        current_price *= (1 + change)
        prices.append(current_price)
    
    # Create OHLCV data
    data = []
    for i, price in enumerate(prices):
        high = price * (1 + abs(np.random.normal(0, 0.001)))
        low = price * (1 - abs(np.random.normal(0, 0.001)))
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        volume = np.random.uniform(100, 1000)
        
        data.append({
            'date': timestamps[i],
            'open': open_price,
            'high': max(open_price, high, close_price),
            'low': min(open_price, low, close_price),
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    
    # Add mock orderflow data
    df['delta'] = np.random.normal(0, 100, length)
    df['cvd'] = df['delta'].cumsum()
    df['bid_volume'] = df['volume'] * np.random.uniform(0.4, 0.6, length)
    df['ask_volume'] = df['volume'] - df['bid_volume']
    
    return df

def test_strategy_initialization():
    """Test strategy initialization"""
    print("\n🔧 Testing strategy initialization...")
    
    try:
        # Mock config
        config = {
            'stake_currency': 'USDT',
            'stake_amount': 1000,
            'max_open_trades': 3
        }
        
        strategy = OrderflowStrategy(config)
        print("✅ Strategy initialized successfully")
        
        # Test parameter access
        print(f"  Delta period: {strategy.delta_period.value}")
        print(f"  CVD period: {strategy.cvd_period.value}")
        print(f"  Min confluence score: {strategy.min_confluence_score.value}")
        print(f"  Strong signal threshold: {strategy.strong_signal_threshold.value}")
        
        return strategy
        
    except Exception as e:
        print(f"❌ Strategy initialization failed: {e}")
        return None

def test_populate_indicators(strategy):
    """Test populate_indicators method"""
    print("\n📊 Testing populate_indicators...")
    
    try:
        # Create mock data
        df = create_mock_dataframe(200)
        
        # Test populate_indicators
        result_df = strategy.populate_indicators(df, {'pair': 'BTC/USDT'})
        
        print(f"✅ populate_indicators completed")
        print(f"  Input columns: {len(df.columns)}")
        print(f"  Output columns: {len(result_df.columns)}")
        
        # Check for key indicators
        expected_indicators = [
            'rsi', 'ema_20', 'ema_50', 'bb_lower', 'bb_upper',
            'orderflow_score', 'signal_strength', 'signal_confidence'
        ]
        
        missing_indicators = [ind for ind in expected_indicators if ind not in result_df.columns]
        if missing_indicators:
            print(f"⚠️  Missing indicators: {missing_indicators}")
        else:
            print("✅ All expected indicators present")
        
        return result_df
        
    except Exception as e:
        print(f"❌ populate_indicators failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_populate_entry_trend(strategy, df):
    """Test populate_entry_trend method"""
    print("\n🎯 Testing populate_entry_trend...")
    
    try:
        result_df = strategy.populate_entry_trend(df, {'pair': 'BTC/USDT'})
        
        # Check for entry signals
        long_entries = result_df['enter_long'].sum() if 'enter_long' in result_df.columns else 0
        short_entries = result_df['enter_short'].sum() if 'enter_short' in result_df.columns else 0
        
        print(f"✅ populate_entry_trend completed")
        print(f"  Long entries: {long_entries}")
        print(f"  Short entries: {short_entries}")
        
        # Check entry tags
        if 'enter_tag' in result_df.columns:
            entry_tags = result_df['enter_tag'].dropna().value_counts()
            if len(entry_tags) > 0:
                print("  Entry tag distribution:")
                for tag, count in entry_tags.items():
                    print(f"    {tag}: {count}")
        
        return result_df
        
    except Exception as e:
        print(f"❌ populate_entry_trend failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_populate_exit_trend(strategy, df):
    """Test populate_exit_trend method"""
    print("\n🚪 Testing populate_exit_trend...")
    
    try:
        result_df = strategy.populate_exit_trend(df, {'pair': 'BTC/USDT'})
        
        # Check for exit signals
        long_exits = result_df['exit_long'].sum() if 'exit_long' in result_df.columns else 0
        short_exits = result_df['exit_short'].sum() if 'exit_short' in result_df.columns else 0
        
        print(f"✅ populate_exit_trend completed")
        print(f"  Long exits: {long_exits}")
        print(f"  Short exits: {short_exits}")
        
        # Check exit tags
        if 'exit_tag' in result_df.columns:
            exit_tags = result_df['exit_tag'].dropna().value_counts()
            if len(exit_tags) > 0:
                print("  Exit tag distribution:")
                for tag, count in exit_tags.items():
                    print(f"    {tag}: {count}")
        
        return result_df
        
    except Exception as e:
        print(f"❌ populate_exit_trend failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_performance_features(strategy):
    """Test performance monitoring features"""
    print("\n⚡ Testing performance features...")
    
    try:
        # Test performance stats
        stats = strategy.get_performance_stats()
        print("✅ Performance stats retrieved:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # Test cache functionality
        df = create_mock_dataframe(50)
        
        # Run indicators twice to test caching
        df1 = strategy.populate_indicators(df, {'pair': 'BTC/USDT'})
        df2 = strategy.populate_indicators(df, {'pair': 'BTC/USDT'})
        
        updated_stats = strategy.get_performance_stats()
        print(f"✅ Cache functionality working")
        print(f"  Cache hit rate: {updated_stats['cache_hit_rate']}")
        
    except Exception as e:
        print(f"❌ Performance features test failed: {e}")
        import traceback
        traceback.print_exc()

def generate_test_report(strategy, final_df):
    """Generate comprehensive test report"""
    print("\n" + "="*60)
    print("📋 STRATEGY VALIDATION REPORT")
    print("="*60)
    
    if final_df is not None:
        print(f"\n✅ OVERALL STATUS: PASSED")
        print(f"📊 Data processed: {len(final_df)} candles")
        print(f"📈 Total columns: {len(final_df.columns)}")
        
        # Signal analysis
        long_signals = final_df['enter_long'].sum() if 'enter_long' in final_df.columns else 0
        short_signals = final_df['enter_short'].sum() if 'enter_short' in final_df.columns else 0
        long_exits = final_df['exit_long'].sum() if 'exit_long' in final_df.columns else 0
        short_exits = final_df['exit_short'].sum() if 'exit_short' in final_df.columns else 0
        
        print(f"\n🎯 SIGNAL SUMMARY:")
        print(f"  Long entries: {long_signals}")
        print(f"  Short entries: {short_signals}")
        print(f"  Long exits: {long_exits}")
        print(f"  Short exits: {short_exits}")
        
        # Performance stats
        stats = strategy.get_performance_stats()
        print(f"\n⚡ PERFORMANCE:")
        print(f"  Cache hit rate: {stats['cache_hit_rate']}")
        print(f"  Total calculations: {stats['total_calculations']}")
        print(f"  Avg calculation time: {stats['avg_calculation_time']}")
        
        print(f"\n💡 RECOMMENDATIONS:")
        print(f"  • Strategy code is functional and ready for backtesting")
        print(f"  • Download historical data to run full backtests")
        print(f"  • Consider parameter optimization for better performance")
        
    else:
        print(f"\n❌ OVERALL STATUS: FAILED")
        print(f"💡 RECOMMENDATIONS:")
        print(f"  • Review error messages above")
        print(f"  • Check strategy code for syntax errors")
        print(f"  • Ensure all dependencies are installed")
    
    print("\n" + "="*60)

def main():
    print("🧪 ORDERFLOW STRATEGY VALIDATION")
    print("="*60)
    
    # Test strategy initialization
    strategy = test_strategy_initialization()
    if not strategy:
        return
    
    # Test populate_indicators
    df_with_indicators = test_populate_indicators(strategy)
    if df_with_indicators is None:
        return
    
    # Test populate_entry_trend
    df_with_entries = test_populate_entry_trend(strategy, df_with_indicators)
    if df_with_entries is None:
        return
    
    # Test populate_exit_trend
    final_df = test_populate_exit_trend(strategy, df_with_entries)
    if final_df is None:
        return
    
    # Test performance features
    test_performance_features(strategy)
    
    # Generate report
    generate_test_report(strategy, final_df)

if __name__ == "__main__":
    main()
