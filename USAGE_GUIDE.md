# OrderflowStrategy Usage Guide

## Table of Contents
1. [Quick Start](#quick-start)
2. [Configuration](#configuration)
3. [Strategy Parameters](#strategy-parameters)
4. [Orderflow Concepts](#orderflow-concepts)
5. [Backtesting](#backtesting)
6. [Live Trading](#live-trading)
7. [Troubleshooting](#troubleshooting)

## Quick Start

### 1. Installation

```bash
# Clone or download the strategy files
# Install dependencies
pip install -r requirements.txt

# Validate strategy syntax
python validate_strategy_syntax.py
```

### 2. Basic Configuration

Copy `config_backtest.json` to `config.json` and update for Bybit:

```json
{
    "exchange": {
        "name": "bybit",
        "key": "your_bybit_api_key",
        "secret": "your_bybit_api_secret",
        "ccxt_config": {
            "options": {
                "defaultType": "linear"
            }
        }
    },
    "trading_mode": "futures",
    "margin_mode": "isolated",
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 1500,
        "scale": 0.4
    }
}
```

### 3. Download Data and Test

```bash
# Validate data availability
python validate_data.py

# Download data and run backtest
python run_backtest.py --download --timerange 20240101-20241201
```

## Configuration

### Exchange Setup

The strategy requires an exchange that supports public trade data:

**Supported Exchanges:**
- **Bybit (recommended for futures)**
- Binance
- Binance US
- Kraken
- OKX

**Bybit Futures USDT Configuration:**
```json
{
    "exchange": {
        "name": "bybit",
        "key": "your_api_key",
        "secret": "your_api_secret",
        "ccxt_config": {
            "enableRateLimit": true,
            "rateLimit": 120,
            "timeout": 30000,
            "options": {
                "defaultType": "linear"
            }
        }
    },
    "trading_mode": "futures",
    "margin_mode": "isolated",
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 1500,
        "scale": 0.4
    }
}
```

### Orderflow Configuration

| Parameter | Description | Default | Range |
|-----------|-------------|---------|-------|
| `use_public_trades` | Enable orderflow data | `true` | boolean |
| `cache_size` | Trade cache size | `1000` | 100-5000 |
| `scale` | Data scaling factor | `0.5` | 0.1-1.0 |

### Risk Management

```json
{
    "max_open_trades": 3,
    "stake_amount": 1000,
    "stoploss": -0.05,
    "trailing_stop": true,
    "trailing_stop_positive": 0.01,
    "protections": [
        {
            "method": "StoplossGuard",
            "lookback_period_candles": 60,
            "trade_limit": 4,
            "stop_duration_candles": 60
        }
    ]
}
```

## Strategy Parameters

### Core Parameters

| Parameter | Description | Default | Optimization Range |
|-----------|-------------|---------|-------------------|
| `delta_period` | Delta moving average period | 20 | 10-50 |
| `cvd_period` | CVD analysis period | 50 | 20-100 |
| `min_confluence_score` | Minimum score for entry | 60 | 40-80 |
| `strong_signal_threshold` | Strong signal threshold | 80 | 70-95 |

### Advanced Parameters

| Parameter | Description | Default | Range |
|-----------|-------------|---------|-------|
| `imbalance_threshold` | Imbalance detection threshold | 3.0 | 2.0-5.0 |
| `volume_threshold` | Volume filter threshold | 1.5 | 1.0-3.0 |
| `divergence_lookback` | Divergence detection period | 20 | 10-50 |
| `exhaustion_threshold` | Exhaustion signal threshold | 0.8 | 0.5-1.0 |

### Parameter Optimization

Use hyperopt for parameter optimization:

```bash
python run_backtest.py --optimize
```

Or manually test ranges:

```python
# In your config
"strategy": {
    "delta_period": {"type": "int", "min": 10, "max": 50, "default": 20},
    "cvd_period": {"type": "int", "min": 20, "max": 100, "default": 50}
}
```

## Orderflow Concepts

### Delta Analysis

**Delta** = Ask Volume - Bid Volume

- **Positive Delta**: More buying pressure
- **Negative Delta**: More selling pressure
- **Delta Momentum**: Rate of change in delta
- **Delta Divergence**: Price vs delta divergence

### Cumulative Volume Delta (CVD)

**CVD** = Running sum of delta values

- **CVD Trend**: Direction of cumulative flow
- **CVD Slope**: Rate of CVD change
- **CVD Divergence**: Price vs CVD divergence
- **CVD Exhaustion**: Extreme CVD levels

### Imbalance Detection

**Imbalance Ratio** = Bid Volume / Ask Volume

- **Bid Imbalance**: Ratio > threshold (bullish)
- **Ask Imbalance**: Ratio < 1/threshold (bearish)
- **Stacked Imbalances**: Consecutive imbalanced levels
- **Imbalance Strength**: Magnitude of imbalance

### Footprint Analysis

- **Volume at Price**: Volume distribution across price levels
- **Point of Control (POC)**: Price with highest volume
- **Value Area**: Price range containing 70% of volume
- **Volume Profile**: Shape analysis of volume distribution

### Confluence Scoring

The strategy combines multiple orderflow signals:

```python
confluence_score = (
    delta_score * 0.25 +
    cvd_score * 0.25 +
    imbalance_score * 0.20 +
    volume_score * 0.15 +
    footprint_score * 0.10 +
    divergence_score * 0.05
)
```

## Backtesting

### Basic Backtesting

```bash
# Quick backtest
freqtrade backtesting --config config_backtest.json --strategy OrderflowStrategy --timerange 20240101-20240301

# Detailed backtest with exports
freqtrade backtesting --config config_backtest.json --strategy OrderflowStrategy --timerange 20240101-20241201 --export trades --breakdown day week month
```

### Automated Backtesting

```bash
# Full automated process
python run_backtest.py --download --timerange 20240101-20241201

# Just run backtest (data already downloaded)
python run_backtest.py

# Parameter optimization
python run_backtest.py --optimize
```

### Backtest Analysis

The strategy provides detailed entry/exit tags for analysis:

**Entry Tags:**
- `confluence_long/short`: Standard confluence signals
- `high_confidence_long/short`: High-confidence signals
- `breakout_long/short`: Breakout signals
- `reversal_long/short`: Reversal signals

**Exit Tags:**
- `orderflow_reversal_long/short`: Orderflow reversal exits
- `profit_target_long/short`: Profit target exits
- `stop_loss_long/short`: Stop loss exits
- `exhaustion_long/short`: Exhaustion exits

### Performance Metrics

Expected performance with proper orderflow data:

- **Win Rate**: 55-65%
- **Profit Factor**: 1.3-1.8
- **Max Drawdown**: <15%
- **Sharpe Ratio**: >1.0
- **Average Trade Duration**: 2-8 hours

## Live Trading

### Pre-Live Checklist

1. **Data Validation**: Ensure orderflow data is available
2. **Paper Trading**: Test with paper trading first
3. **Risk Management**: Set appropriate position sizes
4. **Monitoring**: Set up alerts and monitoring

### Live Configuration

```json
{
    "dry_run": false,
    "stake_amount": 100,
    "max_open_trades": 2,
    "orderflow": {
        "use_public_trades": true,
        "cache_size": 500
    },
    "telegram": {
        "enabled": true,
        "token": "your_bot_token",
        "chat_id": "your_chat_id"
    }
}
```

### Monitoring

Monitor these key metrics:

- **Signal Quality**: Confluence scores and signal strength
- **Cache Performance**: Hit rates and calculation times
- **Trade Performance**: Entry/exit tag distribution
- **Risk Metrics**: Drawdown and position sizes

## Troubleshooting

### Common Issues

**1. No Orderflow Data**
```
WARNING: Orderflow data not available
```
**Solution**: Enable `use_public_trades` in config and download trades data

**2. Poor Performance**
```
Low win rate or high drawdown
```
**Solution**: 
- Check data quality with `python validate_data.py`
- Optimize parameters with `python run_backtest.py --optimize`
- Ensure sufficient trade density (>10 trades/hour)

**3. Memory Issues**
```
High memory usage or slow performance
```
**Solution**:
- Reduce `cache_size` in orderflow config
- Use smaller timeranges for backtesting
- Monitor with `strategy.get_performance_stats()`

**4. No Entry Signals**
```
Strategy not generating trades
```
**Solution**:
- Lower `min_confluence_score` parameter
- Check if orderflow data is available
- Verify exchange supports public trades

### Debug Mode

Enable debug logging:

```json
{
    "logging": {
        "verbosity": 3,
        "logfile": "logs/debug.log"
    }
}
```

### Performance Optimization

```python
# Check performance stats
stats = strategy.get_performance_stats()
print(f"Cache hit rate: {stats['cache_hit_rate']}")
print(f"Avg calculation time: {stats['avg_calculation_time']}")

# Log performance periodically
strategy.log_performance_stats()
```

### Support

For issues and questions:

1. Check the troubleshooting section above
2. Validate your setup with provided scripts
3. Review freqtrade documentation for general issues
4. Check exchange-specific orderflow data availability
